package com.jygjexp.jynx.basic.back.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jygjexp.jynx.admin.api.entity.SysDictItem;
import com.jygjexp.jynx.admin.api.entity.SysUser;
import com.jygjexp.jynx.admin.api.feign.RemoteUserService;
import com.jygjexp.jynx.basic.app.vo.PostMapVo;
import com.jygjexp.jynx.basic.app.vo.UnTakeOrderVo;
import com.jygjexp.jynx.basic.back.api.feign.RemoteTmsService;
import com.jygjexp.jynx.basic.back.api.feign.RemoteUpmsService;
import com.jygjexp.jynx.basic.back.bo.TrackData;
import com.jygjexp.jynx.basic.back.constants.*;
import com.jygjexp.jynx.basic.back.entity.*;
import com.jygjexp.jynx.basic.back.entity.TmsAdditionalServicesEntity;
import com.jygjexp.jynx.basic.back.mapper.*;
import com.jygjexp.jynx.basic.back.model.bo.*;
import com.jygjexp.jynx.basic.back.model.dto.BarCodeDto;
import com.jygjexp.jynx.basic.back.model.vo.*;
import com.jygjexp.jynx.basic.back.model.vo.excel.OrderExcelVo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jygjexp.jynx.basic.back.model.vo.excel.SheinCodeExportVo;
import com.jygjexp.jynx.basic.back.model.vo.excel.SheinPickSignExcelVo;
import com.jygjexp.jynx.basic.back.replacement.ups.model.response.UpsResponseData;
import com.jygjexp.jynx.basic.back.replacement.ups.model.resuest.ShipmentRequestWrapper;
import com.jygjexp.jynx.basic.back.replacement.ups.service.UpsService;
import com.jygjexp.jynx.basic.back.request.PaymentCallbackRequest;
import com.jygjexp.jynx.basic.back.service.*;
import com.jygjexp.jynx.basic.back.tools.*;
import com.jygjexp.jynx.basic.response.LocalizedR;
import com.jygjexp.jynx.common.core.constant.SecurityConstants;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonParser;
import com.google.gson.JsonElement;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.*;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import cn.hutool.poi.excel.*;


/**
 * 订单处理业务类
 *
 * <AUTHOR>
 * @date 2024-10-11 16:51:46
 */
@Service
@Slf4j
public class OrderServiceImpl extends ServiceImpl<SheinCodeMapper, SheinCodeEntity> implements OrderService {

    @Value("${templatePath.templateLablePath}")
    private String path;
    @Autowired
    private OrderNoRecordService recordService;
    @Autowired
    private OrderNoGeneratorService generatorService;
    @Autowired
    private GmapGeocodeMapper gmapGeocodeMapper;
    @Autowired
    private GmapGeocodeService gmapGeocodeService;
    @Autowired
    private PostMapper postMapper;
    @Autowired
    private ApiAuthService apiAuthService;
    @Autowired
    private SheinCodeMapper sheinCodeMapper;
    @Autowired
    private PudoOrderService pudoOrderService;
    @Autowired
    private PostStatService postStatService;
    @Autowired
    private ZtUpsServiceImpl ztUpsService;
    @Autowired
    private PostService postService;
    @Autowired
    private DriverPostMapper driverPostMapper;
    @Autowired
    private PostGroupMapper postGroupMapper;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private CityService cityService;
    @Autowired
    private WarehouseService warehouseService;
    @Autowired
    private PostGroupService postGroupService;
    @Autowired
    private DriverPostService driverPostService;
    @Autowired
    private UniOrderService uniOrderService;
    @Autowired
    private RemoteUserService remoteUserService;
    @Autowired
    private CityPostCodeService orderSupService;
    @Autowired
    private DriverMapper driverMapper;
    @Autowired
    private ApiAuthMapper apiAuthMapper;
    @Autowired
    private WarehouseMapper warehouseMapper;
    @Autowired
    private PostZipService postZipService;
    @Autowired
    private UpsService upsService;
    @Autowired
    private OrderDataService orderDataService;
    @Autowired
    private PushService pushService;
    @Autowired
    private TmsOrderLogService tmsOrderLogService;
    @Autowired
    private SubOrderService subOrderService;
    @Autowired
    private RemoteUpmsService remoteUpmsService;
    @Autowired
    private RemoteTmsService remoteTmsService;

    //希音订单处理
    @Override
    @Transactional
    public JSONObject handleOrder(CreateSheinOrderBo vo) {

        ApiAuthEntity auth = apiAuthService.getAuthByToken(vo.getToken());
        synchronized (Object.class) {
            String shipperPostcode = vo.getShipperPostcode();
            SheinCodeEntity sc = new SheinCodeEntity();
            sc.setSheinCode(vo.getLogiNo());
            sc.setCreateDate(LocalDateTime.now());
            sc.setPostId(0);
            sc.setCustomNo(vo.getCustomNo());
            sc.setCountryId(vo.getCountryId());
            sc.setProvinceId(vo.getProvinceId());
            sc.setCityId(vo.getCityId());
            sc.setAddress(vo.getAddress());
            sc.setZip(vo.getZip());
            sc.setConsignee(vo.getConsignee());
            sc.setMobile(vo.getMobile());
            sc.setPackageWeight(vo.getWeight());
            sc.setPrice(vo.getPrice());
            sc.setOrderTime(OrderTools.transformLocalDateTime(vo.getOrderTime()));
            sc.setEmail(vo.getEmail());
            sc.setAuthId(auth.getId());
            if ("TEMU".equals(vo.getConsignee())) {
                sc.setAuthId(5);
            } else if ("VIVAIA".equals(vo.getConsignee())) {
                sc.setAuthId(6);
            } else if ("KPX".equals(vo.getConsignee())) {
                sc.setAuthId(7);
            } else if ("Bloomchic Return".equals(vo.getConsignee())) {
                sc.setAuthId(8);
            }
            sc.setSenderCountryName(vo.getShipperCountryCode());
            sc.setSenderProvinceName(vo.getShipperProvince());
            sc.setSenderCityName(vo.getShipperCity());
            sc.setSenderAddress(vo.getShipperAddress());
            sc.setSenderPostalcode(shipperPostcode);
            sc.setSenderMobile(vo.getShipperPhone());
            sc.setLabelPath(vo.getLabelPath());
            if (StringUtils.isNotBlank(vo.getSubChannel())) {
                sc.setSubChannel(vo.getSubChannel());
            } else {
                sc.setSubChannel("M");
            }
            //计算来源城市
            if (StringUtils.isNotBlank(shipperPostcode)) {
                sc.setSourceCity(getSourceCity(shipperPostcode));
            }
            sheinCodeMapper.insert(sc);
            orderDataService.setOrderData();
            JSONObject jo = new JSONObject();
            jo.put("code", "0");
            jo.put("errmsg", "ok");
            jo.put("logiNo", vo.getLogiNo());
            jo.put("postalCode", vo.getPostalCode());
            return jo;

        }
    }


    //顺丰订单处理
    @Override
    public JSONObject handleOrder(CreateSfOrderBo vo) {
        ApiAuthEntity token = apiAuthService.getAuthByToken(vo.getAppId());
        SheinCodeEntity order = getOrderByLogiNoAndAuth(vo.getOrderNo(), token.getId());
        JSONObject jo = new JSONObject();
        if (order != null) {
            jo.put("code", "700020");
            jo.put("errmsg", "The order number already exists:" + order.getThirdLogiNo());
            return jo;
        }
        //查询订单是否已经存在
        String returnLogiNo = generalById(4);
        synchronized (Object.class) {
            SheinCodeEntity SFEntity = new SheinCodeEntity();
            SFEntity.setSheinCode(returnLogiNo);
            SFEntity.setCreateDate(LocalDateTime.now());
            SFEntity.setPostId(0);
            SFEntity.setCustomNo(vo.getOrderNo());
            SFEntity.setCountryId(0);
            SFEntity.setProvinceId(0);
            SFEntity.setCityId(0);
            SFEntity.setCountryName(vo.getReceiveRegion());
            SFEntity.setProvinceName(vo.getReceiveProvince());
            SFEntity.setCityName(vo.getReceiveCity());
            SFEntity.setAddress(vo.getReceiveAddress());
            SFEntity.setZip(vo.getReceivePostcode());
            SFEntity.setConsignee(vo.getReceiveName());
            SFEntity.setMobile(vo.getReceiveMobile());
            SFEntity.setWeight(vo.getPackageWeight());
            SFEntity.setPrice(vo.getPrice());
            SFEntity.setThirdLogiNo(vo.getOrderNo());
            SFEntity.setAuthId(token.getId());
            SFEntity.setWeight(vo.getPackageWeight());
            SFEntity.setPackageWidth(vo.getPackageWidth());
            SFEntity.setPackageLength(vo.getPackageLength());
            SFEntity.setPackageHeight(vo.getPackageHeight());
            SFEntity.setSenderAddress(vo.getSenderAddress());
            SFEntity.setSenderCountryName(vo.getSenderRegion());
            SFEntity.setSenderProvinceName(vo.getSenderProvince());
            SFEntity.setSenderCityName(vo.getSenderCity());
            SFEntity.setSenderName(vo.getSenderName());
            SFEntity.setSenderMobile(vo.getSenderMobile());
            SFEntity.setSenderPostalcode(vo.getSenderPostalcode());
            SFEntity.setIsUps(0);
            //计算来源城市
            if (StringUtils.isNotBlank(vo.getSenderPostalcode())) {
                SFEntity.setSourceCity(getSourceCity(vo.getSenderPostalcode()));
            }
            String labelCode = HMACSHA256.sign(returnLogiNo, "NB");
            SFEntity.setLabelCode(labelCode);
            SFEntity.insert();
            jo.put("code", "0");
            jo.put("errmsg", "ok");
            jo.put("nbTracking", SFEntity.getSheinCode());

            //开发环境
            try {
                jo.put("label", "http://api.neighbourexpress.com/zt/api/shipment/label?id=" + URLEncoder.encode(SFEntity.getLabelCode(), "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            //jo.put("label", "http://t.neighbourexpress.com/zt/api/shipment/label?id=" + URLEncoder.encode(sc.getLabelCode(), "UTF-8"));
        }
        return jo;
    }


    //优时派订单处理
    @Override
    public JSONObject handleOrder(YspBo vo) {
        //记录报文
        recordOrderData("YSP:"+vo.getLogiNo(), JSONObject.toJSONString(vo));

        //获取客户token
        ApiAuthEntity token = apiAuthService.getAuthByToken(vo.getToken());
        SheinCodeEntity order = getOrderByLogiNoAndAuth(vo.getLogiNo(), token.getId());
        if (order != null) {
            return renderErr("700020", "The order number already exists " + order.getThirdLogiNo());
        }
        //分配PostCode
        PostEntity postEntity = allocationPostCode(vo.getSenderPostalcode(), null, vo.getSenderProvince(), vo.getSenderCity(), vo.getSenderAddress());
        CityEntity senderProvince = cityService.selectScopeByCity(AreaGradeConstants.province, vo.getSenderProvince());
        CityEntity receiverProvince = cityService.selectScopeByCity(AreaGradeConstants.province, vo.getReceiveProvince());
        if (postEntity == null || senderProvince == null || receiverProvince == null) {
            return renderErr("500", "收件/发件地址无法定位，请检查地址格式是否正确！");
        }

        //创建优时派退件单号
        String returnLogiNo = generalById(AuthConstants.AUTH_YSP);
        //判断发件人邮编是否在覆盖范围
        PostZipEntity record = postZipService.selectByZip(OrderTools.getFirstThreeChars(vo.getSenderPostalcode()));

        UpsResponseData response = null;
        Boolean isUps=false;
        if (record== null){
            isUps=true;
        }

        if (record == null) {
            //创建UPS订单
            ShipmentRequestWrapper upsOrderReturnWrapper = upsService.getUpsOrderReturnWrapper(createUpsPara(vo));
            response = upsService.createUpsOrder(upsOrderReturnWrapper, vo.getLogiNo());
            if (!response.getFlag()) {
                return renderErr("500", response.getMessage());
            }
        }

        try {
            //创建YSP
            SheinCodeEntity yspOrder = saveYspOrderUPS(vo, returnLogiNo, token.getId(), response);
            if (isUps && response.getLabelUrl()!= null) {
                //更新
                UpdateWrapper<SheinCodeEntity> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("id", yspOrder.getId());
                SheinCodeEntity entity = new SheinCodeEntity();
                entity.setLabelOk(1);
                entity.setLabelPath(response.getLabelUrl());
                // 设置 label_ok 为 true
                this.update(entity, updateWrapper);
            }
            //返回结果
            JSONObject jo = new JSONObject();
            jo.put("code", "0");
            jo.put("errmsg", "ok");
            jo.put("nbLogiNo", yspOrder.getSheinCode());
            //设置驿站编码
            jo.put("recommendPostNo", postEntity.getPostNo());
            jo.put("isUps", yspOrder.getIsUps());
            if (yspOrder.getIsUps() == 1) {
                jo.put("upsNo", yspOrder.getUpsWaybillNumber());
            }
            //开发环境
            jo.put("label", "http://api.neighbourexpress.com/zt/api/return/printLabel?id=" + URLEncoder.encode(yspOrder.getLabelCode(), "UTF-8"));
            return jo;
        } catch (Exception e) {
            System.out.println("推送订单发生错误");
        }
        JSONObject jo = new JSONObject();
        jo.put("code", "999980");
        jo.put("errmsg", "save failure");
        return jo;
    }

    @Override
    public String getUpsLabel(String orderNo) {
        String labelBase64 = upsShipmentLabel(orderNo);
        String url = Base64ImageUploader.uploadBase64ImageToOss(labelBase64, "OSS_UPS", orderNo + ".pdf");
        return url;
    }




    //保存订单
    private SheinCodeEntity saveYspOrder(YspBo vo, String returnLogiNo, Integer authId, boolean isUps, String result) throws JSONException {
        CityEntity receiveProvince = cityService.selectScopeByCity(AreaGradeConstants.province, vo.getReceiveProvince());
        CityEntity receiveCity = cityService.selectScopeByCity(AreaGradeConstants.city, vo.getReceiveCity());

        SheinCodeEntity sheinEntity = new SheinCodeEntity();
        String senderPostalCode = vo.getSenderPostalcode();
        BeanUtils.copyProperties(vo, sheinEntity);
        sheinEntity.setSheinCode(returnLogiNo);
        sheinEntity.setCreateDate(LocalDateTime.now());
        sheinEntity.setPostId(0);
        sheinEntity.setCustomNo(vo.getLogiNo());
        sheinEntity.setCountryId(1);
        sheinEntity.setProvinceId(receiveProvince==null?0:receiveProvince.getCityId());
        sheinEntity.setCityId(receiveCity==null?0:receiveCity.getCityId());
        sheinEntity.setCountryName(vo.getReceiveRegion());
        sheinEntity.setProvinceName(vo.getReceiveProvince());
        sheinEntity.setCityName(vo.getReceiveCity());
        sheinEntity.setAddress(vo.getReceiveAddress());
        sheinEntity.setZip(vo.getReceivePostcode());
        sheinEntity.setConsignee(vo.getReceiveName());
        sheinEntity.setMobile(vo.getReceiveMobile());
        sheinEntity.setWeight(vo.getPackageWeight());
        sheinEntity.setThirdLogiNo(vo.getLogiNo());
        sheinEntity.setAuthId(authId);
        sheinEntity.setWeight(vo.getPackageWeight());
        sheinEntity.setPackageWidth(vo.getPackageWidth());
        sheinEntity.setPackageLength(vo.getPackageLength());
        sheinEntity.setPackageHeight(vo.getPackageHeight());
        sheinEntity.setSenderCountryName(vo.getSenderRegion());
        sheinEntity.setSenderProvinceName(vo.getSenderProvince());
        sheinEntity.setSenderCityName(vo.getSenderCity());
        sheinEntity.setIsUps(0);
        sheinEntity.setCreateDate(LocalDateTime.now());
        sheinEntity.setOrderTime(LocalDateTime.now().plusHours(13));

        //计算来源城市
        if (StringUtils.isNotBlank(senderPostalCode)) {
            sheinEntity.setSourceCity(getSourceCity(senderPostalCode));
        }

        if (isUps) {
            //解析返回结果
            org.json.JSONObject resultJson = new org.json.JSONObject(result);
            org.json.JSONObject response = resultJson.getJSONObject("response");
            String upsId = response.getString("id");
            String upsWaybillNumber = response.getString("waybill_number");
            org.json.JSONObject priceMsrp = response.getJSONObject("price").getJSONObject("msrp");
            BigDecimal upsPrice = BigDecimal.valueOf(priceMsrp.getDouble("value"));
            String upsPriceSymbol = priceMsrp.getString("symbol");

            sheinEntity.setUpsId(upsId);
            sheinEntity.setUpsWaybillNumber(upsWaybillNumber);
            sheinEntity.setUpsPrice(upsPrice);
            sheinEntity.setUpsPriceSymbol(upsPriceSymbol);
            sheinEntity.setIsUps(1);

            String yyyyMM = DateTimeFormatter.ofPattern("yyyyMM").format(LocalDate.now());
            File curMonthFolder = new File(TkzjConstants.UNI_SIGN_FILE_PATH + "/ups/" + yyyyMM);
            if (!curMonthFolder.exists()) {
                curMonthFolder.mkdir();
                curMonthFolder.setExecutable(true, false);
                curMonthFolder.setReadable(true, false);
            }
            String labelPath = "/ups/" + yyyyMM + "/" + sheinEntity.getSheinCode() + ".pdf";
            sheinEntity.setLabelPath(labelPath);

            String labelPathYc = "/ups/" + yyyyMM + "/" + sheinEntity.getSheinCode() + ".yicang.pdf";
            sheinEntity.setYcLabelPath(labelPathYc);
        }


        String labelCode = HMACSHA256.sign(sheinEntity.getSheinCode(), "NB");
        sheinEntity.setLabelCode(labelCode);
        //保存订单
        sheinCodeMapper.insert(sheinEntity);
        return sheinEntity;
    }




    //创建UPS订单(VK)
    private R createCpsOrder(YspBo vo) {
        JSONObject data = new JSONObject();
        data.put("service_id", "190"); // 货运服务id，可用action=service查询, 186=UPS Express, 190=UPS Standard
        data.put("payment_method", "account"); // 付款方式，请固定填写：account
        data.put("state", "order"); // 订单状态(选项:open/order)	open:预录状态未付款，未提交 order 已提交并付款状态

        JSONObject initiation = new JSONObject(); // 发货信息
        initiation.put("region_id", vo.getSenderRegion());
        initiation.put("postalcode", vo.getSenderPostalcode());
        initiation.put("name", vo.getSenderName());
        initiation.put("mobile_phone", vo.getSenderMobile());
        initiation.put("province", vo.getSenderProvince()); // 省份名称或代码，加拿大和美国的地址请尽量填写两位英文代码，如：ON,AB...
        initiation.put("city", vo.getSenderCity());
        initiation.put("address", vo.getSenderAddress());
        if (StringUtils.isNotBlank(vo.getSenderAddress2())) {
            initiation.put("address2", vo.getSenderAddress2());
        }
        if (StringUtils.isNotBlank(vo.getSenderAddress3())) {
            initiation.put("address3", vo.getSenderAddress3());
        }

        JSONObject destination = new JSONObject(); // 	收件人信息
        destination.put("region_id", vo.getReceiveRegion());
        destination.put("postalcode", vo.getReceivePostcode());
        destination.put("name", vo.getReceiveName());
        destination.put("mobile_phone", vo.getReceiveMobile());
        destination.put("province", vo.getReceiveProvince());
        destination.put("city", vo.getReceiveCity());
        destination.put("address", vo.getReceiveAddress());
        if (StringUtils.isNotBlank(vo.getReceiveAddress2())) {
            destination.put("address2", vo.getReceiveAddress2());
        }
        if (StringUtils.isNotBlank(vo.getReceiveAddress3())) {
            destination.put("address3", vo.getReceiveAddress3());
        }
        // 2023-04-08 默认按商业地址传
        destination.put("type", "commercial");  // 地址类型(resident:普通民宅，commercial:商业住宅)

        JSONArray packages = new JSONArray();

        JSONObject package1 = new JSONObject();
        package1.put("weight", vo.getPackageWeight()); // 单位：参考您的账号设置，默认：磅
        JSONObject dimension1 = new JSONObject();
        dimension1.put("length", vo.getPackageLength());
        dimension1.put("width", vo.getPackageWidth());
        dimension1.put("height", vo.getPackageHeight());
        package1.put("dimension", dimension1);

        packages.add(package1);

        JSONObject _package = new JSONObject();
        _package.put("type", "parcel");
        _package.put("packages", packages);

        data.put("initiation", initiation);
        data.put("destination", destination);
        data.put("package", _package);

        String result = "";
        try {
            result = VerykshipKit.execute("shipment/create", data.toString());
            ZtUpsEntity upsEntity = new ZtUpsEntity();
            upsEntity.setReturnId(0);
            upsEntity.setReturnJson(result);
            upsEntity.setCreateDate(LocalDateTime.now());
            upsEntity.setThirdLogiNo(vo.getLogiNo());
            ztUpsService.save(upsEntity);

        } catch (Exception e) {
            e.printStackTrace();
        }

        JSONObject retJo = JSON.parseObject(result);
        if (retJo.getBooleanValue("status")) {
            try {
                JSONObject response = retJo.getJSONObject("response");
                String code = response.getJSONObject("state").getString("code");
                if ("open".contentEquals(code)) {
                    return R.failed();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return R.ok(retJo);
        } else {
            return R.failed(retJo.getString("message"));
        }

    }


    //面单处理
    public String upsShipmentLabel(String upsId) {
        JSONObject data = new JSONObject();
        data.put("id", upsId);

        String labelRet = VerykshipKit.execute("shipment/label", data.toString());

        JSONObject ret = JSON.parseObject(labelRet);
        String label = ret.getJSONObject("response").getString("label");

        return label;
    }

    //保存订单
    private SheinCodeEntity saveYspOrderUPS(YspBo vo, String returnLogiNo, Integer authId, UpsResponseData response) {
        SheinCodeEntity sheinEntity = new SheinCodeEntity();
        String senderPostalCode = vo.getSenderPostalcode();
        BeanUtils.copyProperties(vo, sheinEntity);
        sheinEntity.setSheinCode(returnLogiNo);
        sheinEntity.setCreateDate(LocalDateTime.now());
        sheinEntity.setPostId(0);
        sheinEntity.setCustomNo(vo.getLogiNo());
        sheinEntity.setCountryId(0);
        sheinEntity.setProvinceId(0);
        sheinEntity.setCityId(0);
        sheinEntity.setCountryName(vo.getReceiveRegion());
        sheinEntity.setProvinceName(vo.getReceiveProvince());
        sheinEntity.setCityName(vo.getReceiveCity());
        sheinEntity.setAddress(vo.getReceiveAddress());
        sheinEntity.setZip(vo.getReceivePostcode());
        sheinEntity.setConsignee(vo.getReceiveName());
        sheinEntity.setMobile(vo.getReceiveMobile());
        sheinEntity.setWeight(vo.getPackageWeight());
        sheinEntity.setThirdLogiNo(vo.getLogiNo());
        sheinEntity.setAuthId(authId);
        sheinEntity.setWeight(vo.getPackageWeight());
        sheinEntity.setPackageWidth(vo.getPackageWidth());
        sheinEntity.setPackageLength(vo.getPackageLength());
        sheinEntity.setPackageHeight(vo.getPackageHeight());
        sheinEntity.setSenderCountryName(vo.getSenderRegion());
        sheinEntity.setSenderProvinceName(vo.getSenderProvince());
        sheinEntity.setSenderCityName(vo.getSenderCity());
        sheinEntity.setIsUps(0);
        sheinEntity.setCreateDate(LocalDateTime.now());
        sheinEntity.setOrderTime(LocalDateTime.now().plusHours(12));
        sheinEntity.setPushFlag(false);
        //计算来源城市
        if (StringUtils.isNotBlank(senderPostalCode)) {
            sheinEntity.setSourceCity(getSourceCity(senderPostalCode));
        }

        if (response != null) {
            //解析返回结果
            sheinEntity.setUpsId(response.getShipmentId());
            sheinEntity.setUpsWaybillNumber(response.getShipmentId());
            sheinEntity.setUpsPrice(OrderTools.safeToBigDecimal(response.getPrice()));
            sheinEntity.setUpsPriceSymbol("CAD");
            sheinEntity.setIsUps(1);
            sheinEntity.setLabelPath(response.getLabelUrl());
        }
        String labelCode = HMACSHA256.sign(sheinEntity.getSheinCode(), "NB");
        sheinEntity.setLabelCode(labelCode);
        //保存订单
        sheinCodeMapper.insert(sheinEntity);
        return sheinEntity;
    }

    //计算来源城市
    public String getSourceCity(String senderPostalCode) {
        String firstThreeChars = senderPostalCode.length() >= 3 ? senderPostalCode.substring(0, 3) : senderPostalCode;
        LambdaQueryWrapper<CityPostCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CityPostCodeEntity::getCode, firstThreeChars)
                .last("limit 1");
        String cityName = "Other";
        CityPostCodeEntity city = orderSupService.getOne(wrapper);
        if (city != null) {
            cityName = city.getCityName();
        }
        return cityName;
    }


    //构造UPS订单参数
    private SheinCodeEntity createUpsPara(YspBo vo) {
        SheinCodeEntity sheinEntity = new SheinCodeEntity();
        BeanUtils.copyProperties(vo, sheinEntity);
        sheinEntity.setSheinCode(vo.getLogiNo());
        sheinEntity.setCreateDate(LocalDateTime.now());
        sheinEntity.setCustomNo(vo.getLogiNo());
        sheinEntity.setCountryName(vo.getReceiveRegion());
        sheinEntity.setProvinceName(vo.getReceiveProvince());
        sheinEntity.setCityName(vo.getReceiveCity());
        sheinEntity.setAddress(vo.getReceiveAddress());
        sheinEntity.setZip(vo.getReceivePostcode());
        sheinEntity.setConsignee(vo.getReceiveName());
        sheinEntity.setMobile(vo.getReceiveMobile());
        sheinEntity.setWeight(vo.getPackageWeight());
        sheinEntity.setThirdLogiNo(vo.getLogiNo());
        sheinEntity.setAuthId(AuthConstants.AUTH_YSP);
        sheinEntity.setWeight(vo.getPackageWeight());
        sheinEntity.setPackageWidth(vo.getPackageWidth());
        sheinEntity.setPackageLength(vo.getPackageLength());
        sheinEntity.setPackageHeight(vo.getPackageHeight());
        sheinEntity.setSenderCountryName(vo.getSenderRegion());
        sheinEntity.setSenderProvinceName(vo.getSenderProvince());
        sheinEntity.setSenderCityName(vo.getSenderCity());
        sheinEntity.setCreateDate(LocalDateTime.now());
        sheinEntity.setOrderTime(LocalDateTime.now().plusHours(12));
        sheinEntity.setPushFlag(false);
        return sheinEntity;
    }


    //创建UPS订单
//    private R createCpsOrder(YspBo vo) {
//        JSONObject data = new JSONObject();
//        data.put("service_id", "190"); // 货运服务id，可用action=service查询, 186=UPS Express, 190=UPS Standard
//        data.put("payment_method", "account"); // 付款方式，请固定填写：account
//        data.put("state", "order"); // 订单状态(选项:open/order)	open:预录状态未付款，未提交 order 已提交并付款状态
//
//        JSONObject initiation = new JSONObject(); // 发货信息
//        initiation.put("region_id", vo.getSenderRegion());
//        initiation.put("postalcode", vo.getSenderPostalcode());
//        initiation.put("name", vo.getSenderName());
//        initiation.put("mobile_phone", vo.getSenderMobile());
//        initiation.put("province", vo.getSenderProvince()); // 省份名称或代码，加拿大和美国的地址请尽量填写两位英文代码，如：ON,AB...
//        initiation.put("city", vo.getSenderCity());
//        initiation.put("address", vo.getSenderAddress());
//        if (StringUtils.isNotBlank(vo.getSenderAddress2())) {
//            initiation.put("address2", vo.getSenderAddress2());
//        }
//        if (StringUtils.isNotBlank(vo.getSenderAddress3())) {
//            initiation.put("address3", vo.getSenderAddress3());
//        }
//
//        JSONObject destination = new JSONObject(); // 	收件人信息
//        destination.put("region_id", vo.getReceiveRegion());
//        destination.put("postalcode", vo.getReceivePostcode());
//        destination.put("name", vo.getReceiveName());
//        destination.put("mobile_phone", vo.getReceiveMobile());
//        destination.put("province", vo.getReceiveProvince());
//        destination.put("city", vo.getReceiveCity());
//        destination.put("address", vo.getReceiveAddress());
//        if (StringUtils.isNotBlank(vo.getReceiveAddress2())) {
//            destination.put("address2", vo.getReceiveAddress2());
//        }
//        if (StringUtils.isNotBlank(vo.getReceiveAddress3())) {
//            destination.put("address3", vo.getReceiveAddress3());
//        }
//        // 2023-04-08 默认按商业地址传
//        destination.put("type", "commercial");  // 地址类型(resident:普通民宅，commercial:商业住宅)
//
//        JSONArray packages = new JSONArray();
//
//        JSONObject package1 = new JSONObject();
//        package1.put("weight", vo.getPackageWeight()); // 单位：参考您的账号设置，默认：磅
//        JSONObject dimension1 = new JSONObject();
//        dimension1.put("length", vo.getPackageLength());
//        dimension1.put("width", vo.getPackageWidth());
//        dimension1.put("height", vo.getPackageHeight());
//        package1.put("dimension", dimension1);
//
//        packages.add(package1);
//
//        JSONObject _package = new JSONObject();
//        _package.put("type", "parcel");
//        _package.put("packages", packages);
//
//        data.put("initiation", initiation);
//        data.put("destination", destination);
//        data.put("package", _package);
//
//        String result = "";
//        try {
//            result = VerykshipKit.execute("shipment/create", data.toString());
//            ZtUpsEntity upsEntity = new ZtUpsEntity();
//            upsEntity.setReturnId(0);
//            upsEntity.setReturnJson(result);
//            upsEntity.setCreateDate(LocalDateTime.now());
//            upsEntity.setThirdLogiNo(vo.getLogiNo());
//            ztUpsService.save(upsEntity);
//
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        JSONObject retJo = JSON.parseObject(result);
//        if (retJo.getBooleanValue("status")) {
//            try {
//                JSONObject response = retJo.getJSONObject("response");
//                String code = response.getJSONObject("state").getString("code");
//                if ("open".contentEquals(code)) {
//                    return R.failed();
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            return R.ok(retJo);
//        } else {
//            return R.failed();
//        }
//
//    }

    //通过LogiNo、Auth查询单号
    public SheinCodeEntity getOrderByLogiNoAndAuth(String code, Integer auth) {
        LambdaQueryWrapper<SheinCodeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SheinCodeEntity::getThirdLogiNo, code)
                .eq(SheinCodeEntity::getAuthId, auth)
                .last("limit 1");
        return sheinCodeMapper.selectOne(queryWrapper);
    }

    //通过订单号获取订单
    public SheinCodeEntity getOrderByOrderNo(String orderNo) {
        LambdaQueryWrapper<SheinCodeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SheinCodeEntity::getSheinCode, orderNo)
                .last("limit 1");
        return sheinCodeMapper.selectOne(queryWrapper);
    }

    public SheinCodeEntity getOrderByOrderNoYSP(String orderNo) {
        LambdaQueryWrapper<SheinCodeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SheinCodeEntity::getSheinCode, orderNo)
                .or()
                .eq(SheinCodeEntity::getUpsWaybillNumber,orderNo)
                .last("limit 1");
        return sheinCodeMapper.selectOne(queryWrapper);
    }

    //通过面单码获取订单
    public SheinCodeEntity getOrderByLabelCode(String code) {
        LambdaQueryWrapper<SheinCodeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SheinCodeEntity::getLabelCode, code)
                .or()
                .eq(SheinCodeEntity::getLabelCode, "+" + code)
                .last("limit 1");
        return sheinCodeMapper.selectOne(queryWrapper);
    }

    @Override
    public List<SheinCodeEntity> getRecentOrder(Integer postId) {
        // 获取当前时间和三天前的时间
        LocalDateTime threeDaysAgo = LocalDateTime.now().minusDays(3);
        LambdaQueryWrapper<SheinCodeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SheinCodeEntity::getPostId, postId)
                .ge(SheinCodeEntity::getPostInTime, threeDaysAgo)
                .orderByDesc(SheinCodeEntity::getPostInTime);
        return sheinCodeMapper.selectList(queryWrapper);
    }

    //通过司机获取驿站组，通过驿站组获取驿站
    @Override
    public Map<String, Object> getGroupAndPost(Integer driverId) {
        LambdaQueryWrapper<DriverPostEntity> driverWrapper = new LambdaQueryWrapper<>();
        driverWrapper.select(DriverPostEntity::getGroupId)
                .eq(DriverPostEntity::getDriverId, driverId)
                .gt(DriverPostEntity::getGroupId, 0);
        HashMap<String, Object> map = new HashMap<>();
        List<DriverPostEntity> driverPostEntities = driverPostMapper.selectList(driverWrapper);
        //获取对应的驿站
        for (int i = 0; i < driverPostEntities.size(); i++) {
            Integer groupId = driverPostEntities.get(i).getGroupId();
            List<PostEntity> postLists = postGroupService.getPosts(groupId, null);
            String groupName = getGroupName(groupId);
            map.put(groupName, postLists);
        }
        return map;
    }

    //查询司机驿站组以及驿站对应的包裹
    @Override
    public DriverDataVo getDriverOrder(Integer driverId) {
        //获取对应的驿站组
        LambdaQueryWrapper<DriverPostEntity> driverWrapper = new LambdaQueryWrapper<>();
        driverWrapper.select(DriverPostEntity::getGroupId)
                .eq(DriverPostEntity::getDriverId, driverId)
                .gt(DriverPostEntity::getGroupId, 0);
        // 查询并提取 group_id
        List<Integer> groupIds = driverPostMapper.selectList(driverWrapper)
                .stream()
                .map(driverPost -> (Integer) driverPost.getGroupId())
                .collect(Collectors.toList());
        List<PostEntity> postEntities = null;
        if (!groupIds.isEmpty()) {
            LambdaQueryWrapper<PostEntity> postWrapper = new LambdaQueryWrapper<>();
            postWrapper.in(PostEntity::getGroupId, groupIds);
            postEntities = postMapper.selectList(postWrapper);
        }

        //查询驿站退件数量
        HashMap<String, Long> postNumMap = new HashMap<>();
        HashMap<String, Long> groupNumMap = new HashMap<>();
        postEntities = Optional.ofNullable(postEntities).orElse(Collections.emptyList());
        for (int i = 0; i < postEntities.size(); i++) {
            LambdaQueryWrapper<SheinCodeEntity> returnWrapper = new LambdaQueryWrapper<>();
            int postId = postEntities.get(i).getPostId();
            String postName = postEntities.get(i).getPostName();
            Integer groupId = postEntities.get(i).getGroupId();
            String groupName = getGroupName(groupId);
            returnWrapper.eq(SheinCodeEntity::getPostId, postId)
                    .eq(SheinCodeEntity::getStatus, OrderConstants.STATUS_2_POST)
                    .eq(SheinCodeEntity::getHangUp, YesOrNoConstants.NO);
            Long num = sheinCodeMapper.selectCount(returnWrapper);
            postNumMap.put(postName, num);
            if (groupNumMap.containsKey(groupName)) {
                // 如果存在，取出当前的 value 并加上 num，然后再放入 map
                long updatedNum = groupNumMap.get(groupName) + num;
                groupNumMap.put(groupName, updatedNum);
            } else {
                // 如果不存在，直接将 postName 和 num 放入 map
                groupNumMap.put(groupName, num);
            }

        }
        DriverDataVo driverDataVo = new DriverDataVo();
        driverDataVo.setGroupMap(groupNumMap);
        driverDataVo.setPostMap(postNumMap);
        return driverDataVo;
    }

    //查询各类订单数量
    @Override
    public List<returnPreviewVo> getOrderNum() {
        // 获取 Redis 中存储的值
        ValueOperations<String, Object> valueOperations = redisTemplate.opsForValue();
        String data = (String) valueOperations.get("postData");

        if (data != null) {
            try {
                // 创建 ObjectMapper 用于反序列化 JSON 数据
                ObjectMapper objectMapper = new ObjectMapper();
                // 将 JSON 字符串转换为 List<PostPageVo>
                return objectMapper.readValue(data, new TypeReference<List<returnPreviewVo>>() {
                });
            } catch (JsonProcessingException e) {
                System.out.println("getOrderNumJSON解析错误");
            }
        }
        return getPreviewNum();
    }


    //退件预览
    public void returnPreview() {
        ValueOperations<String, Object> valueOperations = redisTemplate.opsForValue();
        ArrayList<returnPreviewVo> previewNum = getPreviewNum();
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonData = "";
        try {
            jsonData = objectMapper.writeValueAsString(previewNum);
        } catch (JsonProcessingException e) {
            System.out.println("退件预览JSON解析错误");
        }
        valueOperations.set("postData", jsonData, 6, TimeUnit.HOURS);

    }

    //退件数量查询
    public ArrayList<returnPreviewVo> getPreviewNum() {
        //只查询大于这个时间的
        String date = "2024-01-01 00:00:00";
        //获取所有有效驿站
        List<PostEntity> postEntities = postMapper.selectList(new LambdaQueryWrapper<PostEntity>().eq(PostEntity::getIsValid, true));
        ArrayList<returnPreviewVo> postList = new ArrayList<>();
        for (PostEntity postEntity : postEntities) {
            Integer postId = postEntity.getPostId();
            LambdaQueryWrapper<SheinCodeEntity> sheinWrapper = new LambdaQueryWrapper<>();
            sheinWrapper.eq(SheinCodeEntity::getStatus, OrderConstants.STATUS_2_POST)
                    .eq(SheinCodeEntity::getAuthId, AuthConstants.AUTH_SHEIN)
                    .ge(SheinCodeEntity::getPostInTime, date)
                    .eq(SheinCodeEntity::getPostId, postId);
            LambdaQueryWrapper<SheinCodeEntity> temuWrapper = new LambdaQueryWrapper<>();
            temuWrapper.eq(SheinCodeEntity::getStatus, OrderConstants.STATUS_2_POST)
                    .eq(SheinCodeEntity::getAuthId, AuthConstants.AUTH_TEMU)
                    .ge(SheinCodeEntity::getPostInTime, date)
                    .eq(SheinCodeEntity::getPostId, postId);
            LambdaQueryWrapper<SheinCodeEntity> otherWrapper = new LambdaQueryWrapper<>();
            otherWrapper.eq(SheinCodeEntity::getStatus, OrderConstants.STATUS_2_POST)
                    .in(SheinCodeEntity::getAuthId, AuthConstants.AUTH_YSP, AuthConstants.AUTH_VIVAIA, AuthConstants.AUTH_KPX,
                            AuthConstants.AUTH_BLOOMCHIC, AuthConstants.AUTH_SF)
                    .ge(SheinCodeEntity::getPostInTime, date)
                    .eq(SheinCodeEntity::getPostId, postId);
            returnPreviewVo previewVo = new returnPreviewVo();
            previewVo.setSheinNum(count(sheinWrapper));
            previewVo.setTemuNum(count(temuWrapper));
            previewVo.setNum(count(otherWrapper));
            // 获取司机信息
            if (postEntity.getGroupId() != null) {
                DriverEntity driver = driverPostService.getDrivers(postEntity.getGroupId());
                String driverName = Optional.ofNullable(driver).map(DriverEntity::getDriverName).orElse("");
                String driverMobile = Optional.ofNullable(driver).map(DriverEntity::getMobile).orElse("");
                previewVo.setDriverName(driverName);
                previewVo.setDriverMobile(driverMobile);
            }
            BeanUtils.copyProperties(postEntity, previewVo);
            postList.add(previewVo);
        }
        return postList;
    }


    //返仓扫描(异步)
    @Override
    @Async
    public R returnScanAsync(String orderNo, Integer warehouseId, String repeatOrder, Boolean localFlag) {
        return returnScan(orderNo, warehouseId, repeatOrder, localFlag);
    }


    //返仓扫描
    @Override
    public R returnScan(String orderNo, Integer warehouseId, String repeatOrder, Boolean localFlag) {
        if (warehouseId == null) {
            R<SysUser> sysUserR = remoteUserService.getOneUserById(SecurityUtils.getUser().getId());

            if (StringUtils.isEmpty(sysUserR.getData().getWarehouseId())) {
                return LocalizedR.failed("bind.the.account.to.a.specific.warehouse");
            }
            try {
                warehouseId = Integer.valueOf(sysUserR.getData().getWarehouseId());
            } catch (Exception e) {
                return LocalizedR.failed("bind.the.account.to.a.specific.warehouse");
            }

        }

        if ((orderNo.startsWith("N") && orderNo.charAt(5) == 'D') ) {
            //正向派送--修改订单的状态并保存轨迹
            remoteTmsService.sortingUpdate(orderNo);
        }

        //处理PUDO订单
        if (orderNo.contains("PU")) {
            if (orderNo.length() > 20) {
                return scanProcess(orderNo, warehouseId, OrderScanConstants.STATUS_2_FAILURE, "订单不存在", "return.order.not.exist");
            }
            //先查询PUDO返仓列表
            PudoOrderEntity pudoOrder = pudoOrderService.getOrderByOrderNo(orderNo);
            if (pudoOrder != null && pudoOrder.getPostInTime() != null) {
                return scanProcess(orderNo, warehouseId, OrderScanConstants.STATUS_2_FAILURE, "重复返仓", "the.order.has.been.returned");
            }
            if (pudoOrder != null && pudoOrder.getDriverTime() != null) {
                LambdaUpdateWrapper<PudoOrderEntity> wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(PudoOrderEntity::getPudoOrderNo, orderNo)
                        .set(PudoOrderEntity::getPostId, 0)
                        .set(PudoOrderEntity::getPostInTime, LocalDateTime.now())
                        .set(PudoOrderEntity::getWarehouseId, warehouseId)
                        .set(PudoOrderEntity::getPudoFlag, StaplesConstants.YES);
                pudoOrderService.update(wrapper);
                return scanProcess(orderNo, warehouseId, OrderScanConstants.STATUS_1_SUCCESS, "", "return.to.warehouse.successfully");
            }
            //插入到PUDO返仓记录
            PudoOrderEntity pudoOrderEntity = new PudoOrderEntity();
            pudoOrderEntity.setPudoOrderNo(orderNo);
            pudoOrderEntity.setPostId(0);
            pudoOrderEntity.setPostInTime(LocalDateTime.now());
            pudoOrderEntity.setWarehouseId(warehouseId);
            pudoOrderEntity.setPudoFlag(StaplesConstants.YES);
            pudoOrderEntity.setLocalFlag(localFlag);
            pudoOrderEntity.insert();
            return scanProcess(orderNo, warehouseId, OrderScanConstants.STATUS_1_SUCCESS, "", "return.to.warehouse.successfully");
        }

        //UNI返仓
        if (!orderNo.contains("NR")) {
            UniOrderEntity uniOrderEntity = uniOrderService.getUniOrderByOrderNo(orderNo);
            if (uniOrderEntity == null) {
                return scanProcess(orderNo, warehouseId, OrderScanConstants.STATUS_2_FAILURE, "订单不存在", "return.order.not.exist");
            }
            UniOrderEntity entity = new UniOrderEntity();
            entity.setReturnCustomerTime(LocalDateTime.now());
            entity.setWarehouseId(warehouseId);
            entity.setOrderId(uniOrderEntity.getOrderId());
            entity.setStatus(UniOrderConstants.STATUS_10_WAREHOUSE);
            uniOrderService.updateById(entity);
            return scanProcess(orderNo, warehouseId, OrderScanConstants.STATUS_1_SUCCESS, "", "return.to.warehouse.successfully");

        }


        //获取对应单号和驿站
        PostEntity post = null;
        SheinCodeEntity sheinOrder = getOrderByOrderNo(orderNo);
        if (sheinOrder == null) {
            return scanProcess(orderNo, warehouseId, OrderScanConstants.STATUS_2_FAILURE, "订单不存在", "return.order.not.exist");
        }
        if (sheinOrder.getPostId() != null) {
            post = postService.getById(sheinOrder.getPostId());
        }
        String postName = "";
        if (post != null) {
            postName = post.getPostName();
        }

        //判断是否外地包裹
        String sourceCity = sheinOrder.getSourceCity();
        if ("Vancouver".equals(sourceCity) || "Toronto".equals(sourceCity)) {
            localFlag = false;
        } else {
            localFlag = true;
        }
        //重复面单返仓
        if ("1".equals(repeatOrder)) {
            //更新状态为已入库
            SheinCodeEntity sheinCodeEntity = new SheinCodeEntity();
            sheinCodeEntity.setId(sheinOrder.getId());
            sheinCodeEntity.setStatus(OrderConstants.STATUS_4_STORAGE);
            sheinCodeEntity.setWarehouseId(warehouseId);
            sheinCodeEntity.setWarehouseTime(LocalDateTime.now());
            sheinCodeEntity.setPostId(sheinOrder.getPostId());
            sheinCodeEntity.updateById();
            return scanProcess(orderNo, warehouseId, OrderScanConstants.STATUS_1_SUCCESS, "", "return.to.warehouse.successfully");
        }

        //判断是否重复面单
        if (sheinOrder.getStatus() >= OrderConstants.STATUS_4_STORAGE && isYesterday(sheinOrder.getWarehouseTime())) {
            return R.failed(2360, "重复面单");
        }

        if (sheinOrder.getStatus() >= OrderConstants.STATUS_4_STORAGE) {
            return scanProcess(orderNo, warehouseId, OrderScanConstants.STATUS_2_FAILURE, "重复返仓", "the.order.has.been.returned");
        }


        //创建-返仓（运营要求只要拿到仓库的包裹都要返仓，并指定返仓的驿站）
        if (sheinOrder.getStatus() == OrderConstants.STATUS_1_CREATE) {
            String note = "创建-返仓";
            //更新状态为已入库
            SheinCodeEntity sheinCodeEntity = new SheinCodeEntity();
            sheinCodeEntity.setId(sheinOrder.getId());
            sheinCodeEntity.setStatus(OrderConstants.STATUS_4_STORAGE);
            sheinCodeEntity.setWarehouseId(warehouseId);
            sheinCodeEntity.setPostInTime(LocalDateTime.now());
            sheinCodeEntity.setWarehouseTime(LocalDateTime.now());
            sheinCodeEntity.setPostId(warehouseId == TkzjConstants.YYZ_POST ? TkzjConstants.YYZ_POST : TkzjConstants.YVR_POST);
            sheinCodeEntity.setNote(note);
            sheinCodeEntity.updateById();
            return scanProcess(orderNo, warehouseId, OrderScanConstants.STATUS_1_SUCCESS, note, "return.to.warehouse.successfully");
        }

        //处理司机取件模式下的返仓
        if (sheinOrder.getStatus() == OrderConstants.STATUS_3_DRIVER) {
            SheinCodeEntity sheinCodeEntity = new SheinCodeEntity();
            sheinCodeEntity.setId(sheinOrder.getId());
            sheinCodeEntity.setStatus(OrderConstants.STATUS_4_STORAGE);
            sheinCodeEntity.setWarehouseId(warehouseId);
            sheinCodeEntity.setWarehouseTime(LocalDateTime.now());
            //记录是否为pudo返仓(该状态下需要判断)
            if (post == null) {
                sheinCodeEntity.setPostId(warehouseId == TkzjConstants.YYZ_POST ? TkzjConstants.YYZ_POST : TkzjConstants.YVR_POST);
                sheinCodeEntity.setPostInTime(LocalDateTime.now());
            }
            if (postName.contains("Staple") & post != null) {
                PudoOrderEntity pudoOrder = new PudoOrderEntity();
                pudoOrder.setPudoOrderNo(orderNo);
                pudoOrder.setPostInTime(LocalDateTime.now());
                pudoOrder.setWarehouseId(warehouseId);
                pudoOrder.setPostId(post.getPostId());
                pudoOrder.setDriverId(sheinOrder.getDriverId());
                pudoOrder.setDriverTime(sheinOrder.getDriverTime());
                pudoOrder.setLocalFlag(localFlag);
                pudoOrder.setPudoFlag(StaplesConstants.NO);
                pudoOrder.insert();
            }
            sheinCodeEntity.updateById();
            return scanProcess(orderNo, warehouseId, OrderScanConstants.STATUS_1_SUCCESS, "", "return.to.warehouse.successfully");
        }

        //处理Staples返仓
        if (sheinOrder.getStatus() == OrderConstants.STATUS_2_POST & postName.contains("Staple")) {
            //进入NB返仓模式
            SheinCodeEntity sheinCodeEntity = new SheinCodeEntity();
            sheinCodeEntity.setId(sheinOrder.getId());
            sheinCodeEntity.setStatus(OrderConstants.STATUS_4_STORAGE);
            sheinCodeEntity.setWarehouseId(warehouseId);
            sheinCodeEntity.setWarehouseTime(LocalDateTime.now());
            sheinCodeEntity.updateById();
            PudoOrderEntity pudoOrder = new PudoOrderEntity();
            pudoOrder.setPudoOrderNo(orderNo);
            pudoOrder.setPostInTime(LocalDateTime.now());
            pudoOrder.setWarehouseId(warehouseId);
            pudoOrder.setPostId(post.getPostId());
            pudoOrder.setDriverId(sheinOrder.getDriverId());
            pudoOrder.setDriverTime(sheinOrder.getDriverTime());
            pudoOrder.setPudoFlag(StaplesConstants.NO);
            pudoOrder.setLocalFlag(localFlag);
            pudoOrder.insert();
            return scanProcess(orderNo, warehouseId, OrderScanConstants.STATUS_1_SUCCESS, "", "return.to.warehouse.successfully");
        }


        //处理驿站收件，司机未收件模式下的返仓(非Staples)
        if (sheinOrder.getStatus() == OrderConstants.STATUS_2_POST & sheinOrder.getDriverId() == null) {
            //进入NB返仓模式
            SheinCodeEntity sheinCodeEntity = new SheinCodeEntity();
            sheinCodeEntity.setId(sheinOrder.getId());
            sheinCodeEntity.setStatus(OrderConstants.STATUS_4_STORAGE);
            sheinCodeEntity.setWarehouseId(warehouseId);
            //设置默认司机(暂时)
            if (warehouseId == TkzjConstants.YYZ_POST) {
                sheinCodeEntity.setDriverId(10209);
            } else {
                sheinCodeEntity.setDriverId(10210);
            }
            sheinCodeEntity.setDriverTime(LocalDateTime.now());
            sheinCodeEntity.setWarehouseTime(LocalDateTime.now());
            sheinCodeEntity.updateById();
            return scanProcess(orderNo, warehouseId, OrderScanConstants.STATUS_1_SUCCESS, "", "return.to.warehouse.successfully");
        }


        //处理非合作驿站
        if (sheinOrder.getPostId() == 419) {
            //更新订单状态
            SheinCodeEntity sheinCodeEntity = new SheinCodeEntity();
            //返回客户仓库
            sheinCodeEntity.setId(sheinOrder.getId());
            sheinCodeEntity.setStatus(OrderConstants.STATUS_4_STORAGE);
            sheinCodeEntity.setWarehouseTime(LocalDateTime.now());
            sheinCodeEntity.setWarehouseId(warehouseId);
            sheinCodeEntity.setNote("非合作驿站返仓");
            sheinCodeEntity.updateById();
            return scanProcess(orderNo, warehouseId, OrderScanConstants.STATUS_1_SUCCESS, "", "return.to.warehouse.successfully");
        }


        //处理驿站收件模式下的返仓（Staples）
        if (sheinOrder.getStatus() == OrderConstants.STATUS_2_POST && Boolean.TRUE.equals(post.getPudo())) {
            SheinCodeEntity sheinCodeEntity = new SheinCodeEntity();
            sheinCodeEntity.setId(sheinOrder.getId());
            sheinCodeEntity.setStatus(OrderConstants.STATUS_4_STORAGE);
            sheinCodeEntity.setWarehouseId(warehouseId);
            sheinCodeEntity.setWarehouseTime(LocalDateTime.now());
            sheinCodeEntity.updateById();
            PudoOrderEntity pudoOrder = new PudoOrderEntity();
            pudoOrder.setPudoOrderNo(orderNo);
            pudoOrder.setPostInTime(LocalDateTime.now());
            pudoOrder.setWarehouseId(warehouseId);
            pudoOrder.setPostId(post.getPostId());
            pudoOrder.setDriverId(sheinOrder.getDriverId());
            pudoOrder.setDriverTime(sheinOrder.getDriverTime());
            pudoOrder.setPudoFlag(StaplesConstants.NO);
            pudoOrder.setLocalFlag(localFlag);
            pudoOrder.insert();
            return scanProcess(orderNo, warehouseId, OrderScanConstants.STATUS_1_SUCCESS, "", "return.to.warehouse.successfully");
        }
        return scanProcess(orderNo, warehouseId, OrderScanConstants.STATUS_2_FAILURE, "订单异常，返仓失败，请联系管理员处理", "return.to.warehouse.error");
    }

    // 处理时区时间
    public LocalDateTime getLocalDateTime(Integer warehouseId) {
        // 查询仓库
        WarehouseEntity warehouse = warehouseService.getById(warehouseId);
        // 获取仓库对应的时区
        String utc = warehouse.getUtc();
        // 获取该时区当前时间
        ZoneId zoneId = ZoneId.of(utc);
        ZonedDateTime zonedDateTime = ZonedDateTime.now(zoneId);
        return zonedDateTime.toLocalDateTime();
    }

    public LocalDateTime getLocalDateTime(Integer warehouseId, LocalDateTime baseTime) {
        // 查询仓库
        WarehouseEntity warehouse = warehouseService.getById(warehouseId);
        // 获取仓库对应的时区
        String utc = warehouse.getUtc();
        ZoneId zoneId = ZoneId.of(utc);
        ZonedDateTime sourceZoned = baseTime.atZone(ZoneId.systemDefault());
        // 转换到目标时区
        ZonedDateTime targetZoned = sourceZoned.withZoneSameInstant(zoneId);

        return targetZoned.toLocalDateTime();
    }



    //返仓扫描处理
    public LocalizedR<Object> scanProcess(String orderNo, Integer warehouseId, Integer scanFlag, String result, String messageKey) {
        ScanHistoryEntity msg = new ScanHistoryEntity(orderNo, warehouseId, scanFlag, result);
        insertHistory(msg);
        return scanFlag == OrderScanConstants.STATUS_1_SUCCESS ? LocalizedR.ok(messageKey, orderNo, msg) : LocalizedR.failed(messageKey, orderNo, msg);
    }

    //新增扫描历史
    public void insertHistory(ScanHistoryEntity scanHistoryEntity) {
        scanHistoryEntity.insert();
    }

    //获取地图所需信息
    public List<PostMapVo> getMapInfo(String postIds) {
        List<Integer> idList = Arrays.stream(postIds.split(";"))
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        QueryWrapper<PostEntity> postQueryWrapper = new QueryWrapper<>();
        postQueryWrapper.in("post_id", idList);
        List<PostEntity> postEntities = postMapper.selectList(postQueryWrapper);
        ArrayList<PostMapVo> list = new ArrayList<>();
        for (PostEntity postEntity : postEntities) {
            Integer postId = postEntity.getPostId();
            PostMapVo postMapVo = new PostMapVo();
            BeanUtils.copyProperties(postEntity, postMapVo);
            postMapVo.setPostInNum(postNormalOrder(postId));
            postMapVo.setDelayNum(postDelayOrder(postId));
            postMapVo.setUniDelayNum(uniOrderService.postDelayOrder(postId));
            list.add(postMapVo);
        }
        return list;
    }

    //补充订单信息
    public void supplementOrderInfo() {
        int offset = 0;
        Integer batchSize = 10000;
        int num = 1;
        while (true) {
            List<SheinCodeEntity> batchRecords = Collections.emptyList();
            // 查询指定范围的数据
            batchRecords = selectListByScope(offset, batchSize);

            // 如果没有更多数据，退出循环
            if (batchRecords.isEmpty()) {
                break;
            }
            //处理当前数据
            for (int i = 0; i < batchRecords.size(); i++) {
                SheinCodeEntity sheinCodeEntity = batchRecords.get(i);
                String senderPostalcode = sheinCodeEntity.getSenderPostalcode();
                if (StringUtils.isBlank(senderPostalcode)) {
                    continue;
                }
                String firstThreeChars = senderPostalcode.length() >= 3 ? senderPostalcode.substring(0, 3) : senderPostalcode;
                LambdaQueryWrapper<CityPostCodeEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(CityPostCodeEntity::getCode, firstThreeChars)
                        .last("limit 1");
                String cityName = "Other";
                CityPostCodeEntity city = orderSupService.getOne(wrapper);
                if (city != null) {
                    cityName = city.getCityName();
                }

                LambdaUpdateWrapper<SheinCodeEntity> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(SheinCodeEntity::getSourceCity, cityName)
                        .eq(SheinCodeEntity::getSheinCode, sheinCodeEntity.getSheinCode());
                sheinCodeMapper.update(null, updateWrapper);

                int percentage = i * 100 / batchRecords.size();

                System.out.println("第+" + num + "批次,当前进度>>>>>>>>>>>>" + percentage + "%");

            }
            num = num + 1;

            // 偏移量增加
            offset += batchSize;
        }
    }


    //补充订单信息
//    public void supplementOrderInfo() {
//        int offset = 0;
//        Integer batchSize = 10000;
//        int num = 1;
//        while (true) {
//            List<SheinCodeEntity> batchRecords = Collections.emptyList();
//            // 查询指定范围的数据
//            batchRecords = selectSupListByScope(offset, batchSize);
//
//            // 如果没有更多数据，退出循环
//            if (batchRecords.isEmpty()) {
//                break;
//            }
//            //处理当前数据
//            for (int i = 0; i < batchRecords.size(); i++) {
//                SheinCodeEntity sheinCodeEntity = batchRecords.get(i);
//                //获取邮编
//                PushEntity pushEntity = pushService.getById(sheinCodeEntity.getSheinCode());
//                String postCode = pushEntity==null?null:pushEntity.getPostCode();
//                if (pushEntity == null){
//                    //通过小包获取
//                     postCode = YspKit.getPostCode(sheinCodeEntity.getSheinCode());
//                    if (StringUtils.isBlank(postCode)){
//                        continue;
//                    }
//                    PushEntity push = new PushEntity();
//                    push.setSheinCode(sheinCodeEntity.getSheinCode());
//                    push.setPostCode(postCode);
//                    pushService.save(push);
//                }
//                LambdaUpdateWrapper<SheinCodeEntity> updateWrapper = new LambdaUpdateWrapper<>();
//                updateWrapper.set(SheinCodeEntity::getSenderPostalcode, postCode)
//                        .eq(SheinCodeEntity::getSheinCode, sheinCodeEntity.getSheinCode());
//                sheinCodeMapper.update(null, updateWrapper);
//
//                int percentage = i * 100 / batchRecords.size();
//
//                System.out.println("第+" + num + "批次,当前进度>>>>>>>>>>>>" + percentage + "%");
//
//            }
//            num = num + 1;
//
//            // 偏移量增加
//            offset += batchSize;
//        }
//    }


    public List<SheinCodeEntity> selectListByScope(Integer start, Integer end) {
        LambdaQueryWrapper<SheinCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.isNull(SheinCodeEntity::getSourceCity)
                .gt(SheinCodeEntity::getCreateDate, "2024-01-01 00:00:01")
                .last("limit " + start + ", " + end);
        return this.list(wrapper);
    }


    public List<SheinCodeEntity> selectSupListByScope(Integer start, Integer end) {
        LambdaQueryWrapper<SheinCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.isNull(SheinCodeEntity::getSenderPostalcode)
                .gt(SheinCodeEntity::getCreateDate, "2024-01-01 00:00:01")
                .eq(SheinCodeEntity::getIsDelete, 0)
                .last("limit " + start + ", " + end);
        return this.list(wrapper);
    }

    //司机扫描
    @Override
    public R driverScan(String orderNo, Integer driverId) {
        if (orderNo.contains("PU")) {
            //插入到PUDO返仓记录
            PudoOrderEntity pudoOrderEntity = new PudoOrderEntity();
            pudoOrderEntity.setPudoOrderNo(orderNo);
            pudoOrderEntity.setDriverTime(LocalDateTime.now());
            pudoOrderEntity.setPudoFlag(StaplesConstants.YES);
            pudoOrderEntity.setDriverId(driverId);
            pudoOrderEntity.insert();
            return LocalizedR.ok("order.scanning.successful", orderNo);
        }

        if (!orderNo.contains("NR")) {
            return driverScanUni(orderNo, driverId);
        }
        //查询订单
        SheinCodeEntity order = getOrder(orderNo);
        if (order == null) {
            return LocalizedR.failed("return.order.not.exist", orderNo);
        }
        if (order.getStatus() == OrderConstants.STATUS_1_CREATE) {
            //根据地址判断对应的驿站
            DriverEntity driver = driverMapper.selectById(driverId);
            String address = driver.getAddress();
            if (StringUtils.isNotBlank(address)) {
                switch (address) {
                    case "551Post":
                        updateDriverOrderScan(order.getId(), driverId, 551);
                        return LocalizedR.ok("order.scanning.successful", orderNo);
                    case "552Post":
                        updateDriverOrderScan(order.getId(), driverId, 552);
                        return LocalizedR.ok("order.scanning.successful", orderNo);
                    case "553Post":
                        updateDriverOrderScan(order.getId(), driverId, 553);
                        return LocalizedR.ok("order.scanning.successful", orderNo);
                    case "554Post":
                        updateDriverOrderScan(order.getId(), driverId, 554);
                        return LocalizedR.ok("order.scanning.successful", orderNo);
                    default:
                        return LocalizedR.failed("post.is.not.scan");
                }
            }

            return LocalizedR.failed("post.is.not.scan");

        }
        if (order.getStatus() == OrderConstants.STATUS_3_DRIVER) {
            return LocalizedR.failed("driver.is.scanned");
        } else if (order.getStatus() == OrderConstants.STATUS_4_STORAGE) {
            return LocalizedR.failed("tkzj.zt.shein.code.the.order.is.storage");
        } else if (order.getStatus() == OrderConstants.STATUS_10_RETURN_CUSTOMER) {
            return LocalizedR.failed("tkzj.zt.shein.code.the.order.is.driver.return");
        }

        //更新订单状态
        SheinCodeEntity sheinCodeEntity = new SheinCodeEntity();
        sheinCodeEntity.setId(order.getId());
        sheinCodeEntity.setStatus(OrderConstants.STATUS_3_DRIVER);
        sheinCodeEntity.setDriverId(driverId);
        sheinCodeEntity.setDriverTime(LocalDateTime.now());
        sheinCodeEntity.updateById();
        return LocalizedR.ok("order.scanning.successful", orderNo);
    }


    public void updateDriverOrderScan(Integer orderId, Integer driverId, Integer postId) {
        SheinCodeEntity sheinCodeEntity = new SheinCodeEntity();
        sheinCodeEntity.setId(orderId);
        sheinCodeEntity.setStatus(OrderConstants.STATUS_3_DRIVER);
        sheinCodeEntity.setDriverId(driverId);
        sheinCodeEntity.setPostInTime(LocalDateTime.now());
        sheinCodeEntity.setPostId(postId);
        sheinCodeEntity.setDriverTime(LocalDateTime.now());
        sheinCodeEntity.updateById();
    }


    public R driverScanUni(String orderNo, Integer driverId) {
        //查询订单
        UniOrderEntity order = getUniOrderByOrderNo(orderNo);
        if (order == null) {
            return LocalizedR.failed("return.order.not.exist", orderNo);
        }
        if (order.getStatus() != UniOrderConstants.STATUS_1_IN_POST) {
            return LocalizedR.failed("post.receipt.status.can.be.scanned", orderNo);
        }
        //更新订单状态
        uniOrderService.driverScan(orderNo, driverId);
        return LocalizedR.ok("order.scanning.successful", orderNo);
    }

    //订单检查
    @Override
    public OldResult checkOrder(CreateSheinOrderBo sheinOrderBo) {
        if (StringUtils.isNotBlank(sheinOrderBo.getLogiNo())) {
            SheinCodeEntity orderByOrderNo = getOrderByOrderNo(sheinOrderBo.getLogiNo());
            if (orderByOrderNo != null) {
                return OldResult.fail("700001", "订单已存在");
            }
        }
        if (StringUtils.isNotBlank(sheinOrderBo.getInternational())) {
            sheinOrderBo.setInternational("N");
        }
        if (sheinOrderBo.getOrderType() == null) {
            sheinOrderBo.setOrderType(0);
        }
        CityEntity cityEntity1 = cityService.selectCityEntity(0, sheinOrderBo.getCountry());
        if (cityEntity1 == null) {
            return OldResult.fail("700015", "country无效");
        }
        sheinOrderBo.setCountryId(cityEntity1.getCityId());
        CityEntity cityEntity2 = cityService.selectCityEntity(1, sheinOrderBo.getProvince());

        if (cityEntity2 == null) {
            return OldResult.fail("700016", "province无效");
        }
        sheinOrderBo.setProvinceId(cityEntity2.getCityId());
        CityEntity cityEntity3 = cityService.selectCityEntity(2, sheinOrderBo.getCity());
        if (cityEntity3 == null) {
            return OldResult.fail("700017", "city无效");
        }
        Date from = Date.from(LocalDateTime.parse(sheinOrderBo.getOrderTime(), DateTimeFormatter.ofPattern("yyy-MM-dd HH:mm:ss")).atZone(ZoneId.systemDefault()).toInstant());
        sheinOrderBo.setOrderTime(from.toString());
        sheinOrderBo.setCityId(cityEntity3.getCityId());
        //分配驿站编码
        PostEntity postEntity = allocationPostCode(sheinOrderBo.getZip(), sheinOrderBo.getCountry(), sheinOrderBo.getProvince(), sheinOrderBo.getCity(), sheinOrderBo.getAddress());
        if (postEntity != null) {
            sheinOrderBo.setPostalCode(postEntity.getPostNo());
            return OldResult.ok("ok", sheinOrderBo);
        }
        return OldResult.fail("700018", "驿站编码不存在");
    }

    //判断是否是昨天或者之前的时间段
    public boolean isYesterday(LocalDateTime warehouseTime) {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 计算 warehouseTime 和当前时间之间的天数差
        long daysDifference = ChronoUnit.DAYS.between(warehouseTime, now);

        // 如果时间差大于等于1天，则说明是昨天或更早的时间
        return daysDifference >= 1;
    }


    //退件单修改
    @Override
    public R updateReturnOrder(SheinCodeEntity sheinCode) {
        String code = sheinCode.getSheinCode();
        //恢复创建状态
        LambdaUpdateWrapper<SheinCodeEntity> updateWrapper = new LambdaUpdateWrapper<>();
        if (0 == sheinCode.getStatus()) {
            // 创建 Wrapper，更新部分字段
            LambdaUpdateWrapper<SheinCodeEntity> updateStateWrapper = new LambdaUpdateWrapper<>();
            updateStateWrapper.eq(SheinCodeEntity::getSheinCode, sheinCode.getSheinCode());
            updateStateWrapper.set(SheinCodeEntity::getStatus, OrderConstants.STATUS_1_CREATE)
                    .set(SheinCodeEntity::getDriverId, null)
                    .set(SheinCodeEntity::getWarehouseId, null)
                    .set(SheinCodeEntity::getWarehouseTime, null)
                    .set(SheinCodeEntity::getDriverTime, null)
                    .set(SheinCodeEntity::getPostInTime, null)
                    .set(SheinCodeEntity::getPostId, null)
                    .set(SheinCodeEntity::getReturnCustomerTime, null);
            return R.ok(this.update(null, updateStateWrapper));
        }
        //删除恢复
        if (0 == sheinCode.getIsDelete() & code.contains("_del")) {
            sheinCode.setSheinCode(code.replace("_del", ""));
            return R.ok(this.updateById(sheinCode));
        }
        updateWrapper.set(SheinCodeEntity::getDriverId, sheinCode.getDriverId())
                .set(SheinCodeEntity::getDriverTime, sheinCode.getDriverTime())
                .set(SheinCodeEntity::getPostId, sheinCode.getPostId())
                .set(SheinCodeEntity::getPostInTime, sheinCode.getPostInTime())
                .set(SheinCodeEntity::getWarehouseTime, sheinCode.getWarehouseTime())
                .set(SheinCodeEntity::getReturnCustomerTime, sheinCode.getReturnCustomerTime());
        updateWrapper.eq(SheinCodeEntity::getSheinCode, sheinCode.getSheinCode());
        return R.ok(this.update(sheinCode, updateWrapper));
    }

    //订单轨迹
    @Override
    public OldResult sheinCodeRoute(@NotNull SheinCodeEntity sheinCode, boolean isSmallPackage) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        JSONArray ja = new JSONArray();
        int status = sheinCode.getStatus();
        if (status >= 1) {
            JSONObject jo = new JSONObject();
            jo.put("status", OrderConstants.STATUS_1_CREATE);
            jo.put("time", sheinCode.getCreateDate().format(formatter));
            jo.put("statusText", TrackConstants.STATUS_1_CREATE);
            jo.put("address", "");
            jo.put("timezone", OrderTools.getTimezone());
            ja.add(jo);

        }
        //优先处理PUDO
        if (status >= 4 & sheinCode.getPostInTime() == null) {
            JSONObject jo = new JSONObject();
            jo.put("status", OrderConstants.STATUS_4_STORAGE);
            jo.put("time", sheinCode.getWarehouseTime().format(formatter));
            jo.put("statusText", TrackConstants.STATUS_4_STORAGE);
            WarehouseEntity warehouse = warehouseService.getById(sheinCode.getWarehouseId());
            CityEntity province = cityService.findCacheById(warehouse.getProvinceId());
            CityEntity city = cityService.findCacheById(warehouse.getCityId());
            jo.put("address", city.getEnName() + " " + province.getEnName());
            jo.put("timezone", OrderTools.getTimezone());
            ja.add(jo);
            if (status < OrderConstants.STATUS_10_RETURN_CUSTOMER) {
                if (isSmallPackage) {
                    JSONObject reJo = new JSONObject();
                    reJo.put("currentStatus", sheinCode.getStatus());
                    reJo.put("isFinish", sheinCode.getStatus() >= 10);
                    reJo.put("route", ja);
                    reJo.put("logiNo", sheinCode.getSheinCode());
                    return OldResult.ok("0", reJo);
                }
                return OldResult.ok("0", ja);
            }
        }
        if (status >= OrderConstants.STATUS_10_RETURN_CUSTOMER & sheinCode.getPostInTime() == null) {
            JSONObject jo = new JSONObject();
            jo.put("status", OrderConstants.STATUS_10_RETURN_CUSTOMER);
            jo.put("time", sheinCode.getReturnCustomerTime().format(formatter));
            jo.put("statusText", TrackConstants.STATUS_10_RETURN_CUSTOMER);
            jo.put("address", "");
            jo.put("timezone", OrderTools.getTimezone());
            ja.add(jo);
            if (isSmallPackage) {
                JSONObject reJo = new JSONObject();
                reJo.put("currentStatus", sheinCode.getStatus());
                reJo.put("isFinish", sheinCode.getStatus() >= 10);
                reJo.put("route", ja);
                reJo.put("logiNo", sheinCode.getSheinCode());
                return OldResult.ok("0", reJo);
            }
            return OldResult.ok("0", ja);

        }
        if (status >= OrderConstants.STATUS_2_POST & sheinCode.getPostInTime() != null) {
            JSONObject jo = new JSONObject();
            jo.put("status", 2);
            jo.put("time", sheinCode.getPostInTime().format(formatter));
            jo.put("statusText", TrackConstants.STATUS_2_POST);
            int postId = sheinCode.getPostId();
            PostEntity post = postService.getById(postId);
            if (post == null) {
                jo.put("address", "");
            } else {
                CityEntity province = cityService.findCacheById(post.getProvinceId());
                CityEntity city = cityService.findCacheById(post.getCityId());
                jo.put("address", city.getEnName() + " " + province.getEnName());
                if (post.getPostId() == 419) {
                    jo.put("address", "");
                }
            }
            jo.put("timezone", OrderTools.getTimezone());
            ja.add(jo);
        }

        //优先处理PUDO
        if (status >= OrderConstants.STATUS_4_STORAGE & sheinCode.getDriverTime() == null) {
            JSONObject jo = new JSONObject();
            jo.put("status", OrderConstants.STATUS_4_STORAGE);
            jo.put("time", sheinCode.getWarehouseTime() == null ? null : sheinCode.getWarehouseTime().format(formatter));
            jo.put("statusText", TrackConstants.STATUS_4_STORAGE);
            WarehouseEntity warehouse = warehouseService.getById(sheinCode.getWarehouseId());
            if (warehouse != null) {
                CityEntity province = cityService.findCacheById(warehouse.getProvinceId());
                CityEntity city = cityService.findCacheById(warehouse.getCityId());
                jo.put("address", city.getEnName() + " " + province.getEnName());
            } else {
                jo.put("address", "");
            }
            jo.put("timezone", OrderTools.getTimezone());
            ja.add(jo);
            if (status < OrderConstants.STATUS_10_RETURN_CUSTOMER) {
                if (isSmallPackage) {
                    JSONObject reJo = new JSONObject();
                    reJo.put("currentStatus", sheinCode.getStatus());
                    reJo.put("isFinish", sheinCode.getStatus() >= 10);
                    reJo.put("route", ja);
                    reJo.put("logiNo", sheinCode.getSheinCode());
                    return OldResult.ok("0", reJo);
                }
                return OldResult.ok("0", ja);
            }
        }

        if (status >= OrderConstants.STATUS_10_RETURN_CUSTOMER & sheinCode.getDriverTime() == null) {
            JSONObject jo = new JSONObject();
            jo.put("status", OrderConstants.STATUS_10_RETURN_CUSTOMER);
            jo.put("time", sheinCode.getReturnCustomerTime().format(formatter));
            jo.put("statusText", TrackConstants.STATUS_10_RETURN_CUSTOMER);
            jo.put("address", "");
            jo.put("timezone", OrderTools.getTimezone());
            ja.add(jo);
            if (isSmallPackage) {
                JSONObject reJo = new JSONObject();
                reJo.put("currentStatus", sheinCode.getStatus());
                reJo.put("isFinish", sheinCode.getStatus() >= 10);
                reJo.put("route", ja);
                reJo.put("logiNo", sheinCode.getSheinCode());
                return OldResult.ok("0", reJo);
            }
            return OldResult.ok("0", ja);
        }
        if (status >= OrderConstants.STATUS_3_DRIVER & sheinCode.getPostInTime() != null) {
            JSONObject jo = new JSONObject();
            jo.put("status", 3);
            jo.put("time", sheinCode.getDriverTime().format(formatter));
            jo.put("statusText", "In Transit");
            jo.put("address", "");
            jo.put("timezone", OrderTools.getTimezone());
            ja.add(jo);
        }
        if (status >= OrderConstants.STATUS_4_STORAGE & sheinCode.getPostInTime() != null) {
            JSONObject jo = new JSONObject();
            jo.put("status", OrderConstants.STATUS_4_STORAGE);
            jo.put("time", sheinCode.getWarehouseTime() == null ? null : sheinCode.getWarehouseTime().format(formatter));
            jo.put("statusText", TrackConstants.STATUS_4_STORAGE);
            WarehouseEntity warehouse = warehouseService.getById(sheinCode.getWarehouseId());
            if (warehouse != null) {
                CityEntity province = cityService.findCacheById(warehouse.getProvinceId());
                CityEntity city = cityService.findCacheById(warehouse.getCityId());
                jo.put("address", city.getEnName() + " " + province.getEnName());
            } else {
                jo.put("address", "");
            }
            jo.put("timezone", OrderTools.getTimezone());
            ja.add(jo);
        }

        if (status >= OrderConstants.STATUS_10_RETURN_CUSTOMER & sheinCode.getPostInTime() != null) {
            JSONObject jo = new JSONObject();
            jo.put("status", OrderConstants.STATUS_10_RETURN_CUSTOMER);
            jo.put("time", sheinCode.getReturnCustomerTime().format(formatter));
            jo.put("statusText", TrackConstants.STATUS_10_RETURN_CUSTOMER);
            jo.put("address", "");
            jo.put("timezone", OrderTools.getTimezone());
            ja.add(jo);
        }
        if (isSmallPackage) {
            JSONObject jo = new JSONObject();
            jo.put("currentStatus", sheinCode.getStatus());
            jo.put("isFinish", sheinCode.getStatus() >= 10);
            jo.put("route", ja);
            jo.put("logiNo", sheinCode.getSheinCode());
            return OldResult.ok("0", jo);
        }
        return OldResult.ok("0", ja);
    }

    //17Track获取路由信息
    @Override
    public Map<String, Object> getTrackBy17Track(SheinCodeEntity sheinCode) {
        OldResult oldResult = sheinCodeRoute(sheinCode, false);
        ObjectMapper mapper = new ObjectMapper();
        String json = null;
        List<TrackData> allTracks = null;
        try {
            json = mapper.writeValueAsString(oldResult.getData());
            allTracks  = mapper.readValue(json, new TypeReference<List<TrackData>>() {});
        } catch (JsonProcessingException e) {
            System.out.println("Track方法JSON解析错误");
        }

        Map<String, Object> result = new LinkedHashMap<>();
        List<Map<String, Object>> events = new ArrayList<>();
        for (TrackData track : allTracks) {
            Map<String, Object> event = new LinkedHashMap<>();
            event.put("location",track.getAddress());
            event.put("time", track.getTime());
            event.put("content",track.getStatusText());
            events.add(event);
        }

        result.put("number", sheinCode.getSheinCode());
        result.put("oriNumber", sheinCode.getCustomNo());
        result.put("destCountry","CA");
        result.put("oriCountry", "CA");
        result.put("oriChannel", "Neighbour Express");
        result.put("events", events);
        result.put("status",OrderConstants.getStatusText(sheinCode.getStatus()));
        return result;
    }

    //统计驿站入站数量(有退件)
    @Override
    public void countPostData() {
        QueryWrapper<SheinCodeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("post_id", "COUNT(1) AS count")
                .gt("post_id", 0)
                .between("post_in_time", OrderTools.getYesterdayStart(), OrderTools.getYesterdayEnd())
                .groupBy("post_id");
        //入站数据
        List<Map<String, Object>> postInResultMap = this.listMaps(queryWrapper);
        List<PostStatEntity> postCounts = new ArrayList<>();
        //出站数据
        List<Map<String, Object>> postOutResultMap = countPostOut();
        //统计
        for (Map<String, Object> record : postInResultMap) {
            Object postId = record.get("post_id");
            PostStatEntity postCount = new PostStatEntity();
            postCount.setPostId(((Number) postId).intValue());
            postCount.setReturnOrderTotal(((Number) record.get("count")).intValue());
            for (Map<String, Object> resultMap : postOutResultMap) {
                if (resultMap.get("post_id").equals(postId)) {
                    int count = Optional.ofNullable(resultMap.get("count"))
                            .filter(Number.class::isInstance)
                            .map(Number.class::cast)
                            .map(Number::intValue)
                            .orElse(0);
                    postCount.setReturnOutOrderTotal(count);

                }
            }
            postCount.setCreateDate(LocalDateTime.now());
            postCount.setDate(LocalDate.parse(OrderTools.getFormattedDateMinusDays("yyyy-MM-dd", 1)));
            postCounts.add(postCount);
        }
        postStatService.batchInsert(postCounts);

    }

    //统计无退件驿站
    public void countPostDataIsZero() {
        LocalDate date = LocalDate.parse(OrderTools.getFormattedDateMinusDays("yyyy-MM-dd", 1));
        LambdaQueryWrapper<PostStatEntity> satWrapper = new LambdaQueryWrapper<>();
        satWrapper.select(PostStatEntity::getPostId)
                .eq(PostStatEntity::getDate, date);
        List<Object> satLists = postStatService.listObjs(satWrapper);
        //无数据驿站
        LambdaQueryWrapper<PostEntity> zeroPostWrapper = new LambdaQueryWrapper<>();
        zeroPostWrapper.select(PostEntity::getPostId)
                .eq(PostEntity::getIsValid, YesOrNoConstants.YES)
                .notIn(!satLists.isEmpty(), PostEntity::getPostId, satLists);
        List<Object> zeroLists = postService.listObjs(zeroPostWrapper);
        List<PostStatEntity> postCounts = new ArrayList<>();
        for (Object postId : zeroLists) {
            PostStatEntity postCount = new PostStatEntity();
            postCount.setPostId((Integer) postId);
            postCount.setReturnOrderTotal(0);
            postCount.setReturnOutOrderTotal(0);
            postCount.setDate(date);
            postCount.setCreateDate(LocalDateTime.now());
            postCounts.add(postCount);
        }
        postStatService.batchInsert(postCounts);
        checkPostStatOrder();
    }

    //驿站数据校验
    private void checkPostStatOrder() {
        LocalDate date = LocalDate.parse(OrderTools.getFormattedDateMinusDays("yyyy-MM-dd", 1));
        LambdaQueryWrapper<PostStatEntity> wrapperStat = new LambdaQueryWrapper<>();
        wrapperStat.eq(PostStatEntity::getDate, date)
                .and(w -> w.eq(PostStatEntity::getReturnOrderTotal, 0)
                        .or()
                        .isNull(PostStatEntity::getReturnOutOrderTotal));
        List<PostStatEntity> postStatEntities = postStatService.list(wrapperStat);
        for (PostStatEntity postStatEntity : postStatEntities) {
            //获取该驿站司机取件数量
            LambdaQueryWrapper<SheinCodeEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SheinCodeEntity::getPostId, postStatEntity.getPostId())
                    .between(SheinCodeEntity::getDriverTime, OrderTools.getYesterdayStart(), OrderTools.getYesterdayEnd());
            long count = Optional.ofNullable(this.count(wrapper)).orElse(0L);
            LambdaUpdateWrapper<PostStatEntity> postStatEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            postStatEntityLambdaUpdateWrapper.eq(PostStatEntity::getStatId, postStatEntity.getStatId())
                    .set(PostStatEntity::getReturnOutOrderTotal, count);
            postStatService.update(postStatEntityLambdaUpdateWrapper);
        }
    }


    //根据订单号查询驿站订单
    @Override
    public R getPostOrder(String orderNo, Integer postId) {
        LocalDateTime currentDateMinusTwoDay = LocalDateTime.now().minusHours(48);
        LambdaQueryWrapper<SheinCodeEntity> orderWrapper = new LambdaQueryWrapper<>();
        orderWrapper.eq(SheinCodeEntity::getSheinCode, orderNo)
                .eq(SheinCodeEntity::getPostId, postId);
        SheinCodeEntity sheinCode = this.getOne(orderWrapper);
        if (sheinCode == null) {
            return LocalizedR.failed("return.order.not.exist", Optional.ofNullable(orderNo));
        }
        Map<Integer, String> map = new HashMap<>();
        if (sheinCode.getStatus() >= OrderConstants.STATUS_3_DRIVER) {
            map.put(PostConstants.STATUS_2_DRIVER, sheinCode.getSheinCode());
            return R.ok(map);
        }
        if (currentDateMinusTwoDay.isAfter(sheinCode.getPostInTime())) {
            map.put(PostConstants.STATUS_1_POST, sheinCode.getSheinCode());
            return R.ok(map);
        }
        map.put(PostConstants.STATUS_3_TIMEOUT, sheinCode.getSheinCode());
        return R.ok(map);
    }

    @Override
    public void updateDamagedByOrderNo(String orderNo, Integer damaged) {
        LambdaUpdateWrapper<SheinCodeEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SheinCodeEntity::getSheinCode, orderNo)
                .set(SheinCodeEntity::getDamaged, damaged);
        this.update(updateWrapper);
    }

    @Override
    public void updateDamagedURL(String orderNo, String damagedURL) {
        LambdaUpdateWrapper<SheinCodeEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SheinCodeEntity::getSheinCode, orderNo)
                .set(SheinCodeEntity::getDamagedUrl, damagedURL);
        this.update(updateWrapper);
    }

    //打印优时派面单
    @Override
    public void printYspLabel(String id, HttpServletResponse response) {
        if (StrUtil.isBlank(id)) {
            return;
        }
        SheinCodeEntity code = getOrderByLabelCode(id);
        if (code == null) {
            return;
        }

        if (code.getIsUps() == 1) {
            PdfUtils.renderPdfFromUrl(response, code.getLabelPath());
        } else {
            Map<String, String> params = Maps.newHashMap();
            params.put("to", code.getConsignee());

            String address = code.getAddress();
            if ("265 Hood Road Unit1".equals(address)) {
                address = "235 Hood Rd Unit 2";
            }
            params.put("toAddress", address + "," + code.getCityName() + "," + code.getProvinceName() + "," + code.getCountryName() + " " + code.getZip());
            params.put("toMobile", "00000");
            String yyz = "YYZ";
            if ("AB".equalsIgnoreCase(code.getProvinceName()) || "Alberta".equalsIgnoreCase(code.getProvinceName())) {
                yyz = "YVR";
            }
            if ("BC".equalsIgnoreCase(code.getProvinceName()) || "British Columbia".equalsIgnoreCase(code.getProvinceName())) {
                yyz = "YVR";
            }

            params.put("from", code.getSenderName());
            params.put("fromAddress", code.getSenderAddress() +
                    (StringUtils.isBlank(code.getSenderAddress2()) ? "" : (" " + code.getSenderAddress2())) +
                    (StringUtils.isBlank(code.getSenderAddress3()) ? "" : (" " + code.getSenderAddress3())) +
                    code.getSenderCityName() + "," + code.getSenderProvinceName() + "," + code.getSenderCountryName() + " " + code.getSenderPostalcode());
            params.put("fromMobile", code.getSenderMobile());

            params.put("logiNo", code.getSheinCode());
            params.put("createDate", DateFormatUtils.format(Date.from(code.getCreateDate()
                    .atZone(ZoneId.systemDefault()).toInstant()), "yyyy-MM-dd"));
            params.put("YYZ", yyz);
            BarCodeDto dto1 = new BarCodeDto(code.getSheinCode(), false, 1.4f, 55, 26, 200);
            dto1.setFontSize(14f);
            dto1.setBaseLine(20f);
            BarCodeDto dto2 = new BarCodeDto(code.getSheinCode(), false, 1.0f, 35, 112, 42);
            dto2.setFontSize(10f);
            dto2.setBaseLine(10f);
            List<BarCodeDto> barCodes = Lists.newArrayList(dto1, dto2);
            String projectRoot = System.getProperty("user.dir");
            response.setContentType("application/pdf");
            //判断当前环境，测试用
            String osName = System.getProperty("os.name").toLowerCase();
            if (osName.contains("win")) {
                path = projectRoot + File.separator + "jynx-basic" + File.separator + "jynx-basic-biz" + File.separator + "tpl" + File.separator + "label_uspeed_20211109_2.pdf";
            }
            PdfUtils.toResponseStream(path, params, barCodes, response);
        }

    }


    //打印顺丰面单
    @Override
    public void printSfLabel(String id, HttpServletResponse response) {
        if (StringUtils.isBlank(id)) {
            return;
        }
        id = id.replace(" ", "+");
        LambdaQueryWrapper<SheinCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SheinCodeEntity::getLabelCode, id)
                .last("limit 1");
        SheinCodeEntity code = this.getOne(wrapper);
        if (code == null) {
            return;
        }
        //设置面单填充参数
        Map<String, String> params = Maps.newHashMap();
        params.put("to", code.getConsignee());
        params.put("toAddress", code.getAddress() + "," + code.getCityName() + "," + code.getProvinceName() + "," + code.getCountryName() + " " + code.getZip());
        params.put("toMobile", code.getMobile());
        String yyz = "YYZ";
        if ("AB".equalsIgnoreCase(code.getProvinceName()) || "Alberta".equalsIgnoreCase(code.getProvinceName())) {
            yyz = "YVR";
        }
        if ("BC".equalsIgnoreCase(code.getProvinceName()) || "British Columbia".equalsIgnoreCase(code.getProvinceName())) {
            yyz = "YVR";
        }
        params.put("from", code.getSenderName());
        params.put("fromAddress", code.getSenderAddress() +
                (StringUtils.isBlank(code.getSenderAddress2()) ? "" : (" " + code.getSenderAddress2())) +
                (StringUtils.isBlank(code.getSenderAddress3()) ? "" : (" " + code.getSenderAddress3())) +
                code.getSenderCityName() + "," + code.getSenderProvinceName() + "," + code.getSenderCountryName() + " " + code.getSenderPostalcode());
        params.put("fromMobile", code.getSenderMobile());

        params.put("logiNo", code.getSheinCode());
        params.put("createDate", DateFormatUtils.format(Date.from(code.getCreateDate()
                .atZone(ZoneId.systemDefault()).toInstant()), "yyyy-MM-dd"));
        params.put("YYZ", yyz);
        String projectRoot = System.getProperty("user.dir");
        response.setContentType("application/pdf");
        //设置PDF参数
        BarCodeDto dto1 = new BarCodeDto(code.getSheinCode(), false, 1.4f, 55, 33, 165);
        dto1.setFontSize(14f);
        dto1.setBaseLine(25f);
        BarCodeDto dto2 = new BarCodeDto(code.getSheinCode(), false, 0.88f, 35, 140, 8);
        dto2.setFontSize(10f);
        dto2.setBaseLine(10f);
        List<BarCodeDto> barCodes = Lists.newArrayList(dto1, dto2);
        //生成面单
        String URL = "https://nbexpress.oss-us-east-1.aliyuncs.com/tmsFile/label_sf_20220316.pdf";
        PdfUtils.toResponseStream(URL, params, barCodes, response);

    }

    //打印中大件、卡派面单
    @Override
    public void printKpLabel(String trackNo, String typeCode, HttpServletResponse response) {
        float width1 = 1.2f;int height1 = 50, margin1 = 20, dpi1 = 295;
        float width2 = 0.8f;int height2 = 35, margin2 = 10, dpi2 = 35;
        //获取订单相关信息
        TmsCustomerOrderEntity customerOrder =
                trackNo.length() >= 15 ? remoteTmsService.getCustomerOrderBySubOrder(trackNo) : remoteTmsService.getCustomerOrder(trackNo, false);
        if (customerOrder == null || customerOrder.getId() == null) {
            return;
        }


        //设置面单填充参数
        String origin = customerOrder.getOrigin();
        String[] splitOrigin = origin.split("/");
        String destination = customerOrder.getDestination();
        String[] splitDestination = destination.split("/");
        Map params = new HashMap<String, String>();
        params.put("s_name", customerOrder.getShipperName());
        params.put("s_address", customerOrder.getShipperAddress() + " " + splitOrigin[2] + " " + splitOrigin[1] + " " + splitOrigin[0]);
        params.put("s_zip", customerOrder.getShipperPostalCode());
        params.put("s_mobile", customerOrder.getShipperPhone());
        params.put("t_mobile", customerOrder.getReceiverPhone());
        params.put("t_zip", customerOrder.getDestPostalCode());
        params.put("t_name", customerOrder.getReceiverName());
        params.put("ref_no", customerOrder.getCustomerOrderNumber().contains("KH") ? "" : customerOrder.getCustomerOrderNumber());
        params.put("order_time", Optional.ofNullable(customerOrder.getCreateTime())
                .map(time -> time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .orElse(null));
        params.put("t_address", customerOrder.getDestAddress() + " " + splitDestination[2] + " " + splitDestination[1] + " " + splitDestination[0]);
        LocalDate startDate = customerOrder.getEstimatedArrivalTimeStart() != null ? customerOrder.getEstimatedArrivalTimeStart().toLocalDate() : null;
        LocalDate endDate = customerOrder.getEstimatedArrivalTimeEnd() != null ? customerOrder.getEstimatedArrivalTimeEnd().toLocalDate() : null;

        params.put("d_date", startDate == null && endDate == null ? "" : startDate != null && endDate != null ? (startDate.equals(endDate) ? startDate.toString() : startDate + " - " + endDate) : (startDate != null ? startDate.toString() : endDate.toString()));
        params.put("instructions", customerOrder.getRemark());
        params.put("type", customerOrder.getCargoType() == 1 ? "Normal" : "Dangerous");
        params.put("type_code", typeCode);

        //获取路线相关信息
        String firstThree = Optional.ofNullable(customerOrder.getDestPostalCode())
                .filter(code -> code.length() >= 3)
                .map(code -> code.substring(0, 3))
                .orElse(customerOrder.getDestPostalCode());

        String routeNo = "";
        try {
            routeNo = remoteTmsService.getRouteNo(null, firstThree);
        } catch (Exception e) {
            System.out.println("获取路线信息异常");
        }
        params.put("pickNo", routeNo);
        response.setContentType("application/pdf");


       String osName = System.getProperty("os.name").toLowerCase();
        String projectRoot = System.getProperty("user.dir");
        String CA_URL = "https://nbexpress.oss-us-east-1.aliyuncs.com/tmsFile/new_kp_20250630(1).pdf";
        if (osName.contains("win")) {
            CA_URL = projectRoot + File.separator + "jynx-basic" + File.separator + "jynx-basic-biz" + File.separator + "tpl" + File.separator + "new_kp_20250630(1).pdf";
        }

        //一票多件信息
        ArrayList<String> qrCodeList = new ArrayList<>();
        if (customerOrder.getCargoInfoEntityList() != null && !customerOrder.getCargoInfoEntityList().isEmpty()) {
            ArrayList<String> templates = new ArrayList<>();
            List<Map<String, String>> paramsList = new ArrayList<>();
            List<List<BarCodeDto>> barCodesList = new ArrayList<>();
            for (int i = 1; i <= customerOrder.getCargoInfoEntityList().size(); i++) {
                String orderNo = customerOrder.getEntrustedOrderNumber().length() >= 15 ? customerOrder.getEntrustedOrderNumber() : customerOrder.getEntrustedOrderNumber() + String.format("%03d", i);
                if (customerOrder.getIsCustomerLabel()){
                    orderNo=customerOrder.getCustomerOrderNumber();
                    width1 = 1.0f; height1 = 45; margin1 = 15; dpi1 = 295;
                    width2 = 0.68f; height2 = 30; margin2 = 8; dpi2 = 32;
                }
                params.put("t_order", orderNo);
                params.put("YYZ", i + "/" +customerOrder.getCargoInfoEntityList().size());
                params.put("weight", OrderTools.formatDecimal(customerOrder.getCargoInfoEntityList().get(i - 1).getWeight(), 2) + " kg");
                templates.add(CA_URL);
                // 复制原有的 params
                Map<String, String> newParams = new HashMap<>(params);
                paramsList.add(newParams);
                BarCodeDto barPara1 = new BarCodeDto(orderNo, false, width1, height1, margin1, dpi1);
                barPara1.setFontSize(12f);
                barPara1.setBaseLine(16f);
                BarCodeDto barPara2 = new BarCodeDto(orderNo, false, width2, height2, margin2, dpi2);
                barPara2.setFontSize(12f);
                barPara2.setBaseLine(16f);
                List<BarCodeDto> barParaList = Lists.newArrayList(barPara1, barPara2);
                barCodesList.add(barParaList);
                try {
                    String base64QRCode = QRCodeGenerator.generateQRCodeBase64(orderNo, 30, 30);
                    qrCodeList.add(base64QRCode);
                } catch (Exception e) {
                    System.err.println("二维码生成失败: " + e.getMessage());
                }
            }
            PdfUtilsToMany.mergeAndOutputPdf(templates, paramsList, barCodesList, qrCodeList, new QrCodePara(160L, 35L, 80L, 80L), response);
        }

    }

    //打印容器标签面单
    @Override
    public void printCageLabel(String labelNos, HttpServletResponse response) {
        if (StringUtils.isBlank(labelNos)) {
            return;
        }

        // 获取订单相关信息
        List<TmsLabelEntity> byLabelCode = remoteTmsService.getByLabelCode(labelNos);
        if (CollUtil.isEmpty(byLabelCode)) {
            return;
        }

        // 初始化需要批量打印的参数集合
        ArrayList<String> templates = new ArrayList<>();
        List<Map<String, String>> paramsList = new ArrayList<>();
        List<List<BarCodeDto>> barCodesList = new ArrayList<>();
        ArrayList<String> qrCodeList = new ArrayList<>();

        // 模板地址
//        String URL = "https://nbexpress.oss-us-east-1.aliyuncs.com/tmsFile/20250421_warehouse_label.pdf";
        String URL = "https://nbexpress.oss-us-east-1.aliyuncs.com/tmsFile/20250424_cage_label.pdf";

        for (TmsLabelEntity tmsLabelEntity : byLabelCode) {
            // 根据仓库id查询仓库名称
            String belongWarehouse = remoteTmsService.getWarehouseName(tmsLabelEntity.getSiteId());
            String originWarehouse = remoteTmsService.getWarehouseName(tmsLabelEntity.getStartSiteId());
            String destWarehouse = remoteTmsService.getWarehouseName(tmsLabelEntity.getEndSiteId());

            // 面单参数
            Map<String, String> params = new HashMap<>();
            params.put("belong_warehouse", belongWarehouse);
            params.put("origin_warehouse", originWarehouse);
            params.put("dest_warehouse", destWarehouse);
            params.put("label_no", tmsLabelEntity.getLabelCode());

            paramsList.add(params);           // 添加到列表
            templates.add(URL);               // 每一条都使用同一模板

            // 条码参数
            BarCodeDto barPara1 = new BarCodeDto(tmsLabelEntity.getLabelCode(), false, 1.2f, 40, 20, 210);
            //BarCodeDto barPara1 = new BarCodeDto(tmsLabelEntity.getLabelCode(), false, 1.2f, 40, 20, 150);
            barPara1.setFontSize(12f);
            barPara1.setBaseLine(16f);
            barCodesList.add(Collections.singletonList(barPara1));

            // 二维码
            try {
                String base64QRCode = QRCodeGenerator.generateQRCodeBase64(tmsLabelEntity.getLabelCode(), 1, 1);
                qrCodeList.add(base64QRCode);
            } catch (Exception e) {
                qrCodeList.add("");  // 防止个别失败导致 list 不对齐
                System.err.println("二维码生成失败: " + e.getMessage());
            }
        }

        try {
            // 设置响应类型为 PDF
            response.setContentType("application/pdf");

            // 调用批量合并方法：一次性输出多页 PDF
            PdfUtilsToMany.mergeAndOutputPdf(
                    templates,
                    paramsList,
                    barCodesList,
                    qrCodeList,
                    new QrCodePara(175L, 263L, 95L, 95L),
                    //new QrCodePara(100L, 230L, 95L, 95L),
                    response
            );
        } catch (Exception e) {
            System.err.println("PDF生成失败: " + e.getMessage());
        }
    }

    // 打印格口编码面单
    @Override
    public void printGridLabel(Long[] gridIds, HttpServletResponse response) {
//        String gridIdNos = Arrays.toString(gridIds);
//        if (StringUtils.isBlank(gridIdNos)) {
//            return;
//        }

        // 获取格口相关信息
        List<TmsSortingGridEntity> gridList = remoteTmsService.getByGridIdNos(gridIds);
        if (CollUtil.isEmpty(gridList)) {
            return;
        }

        // 初始化需要批量打印的参数集合
        ArrayList<String> templates = new ArrayList<>();
        List<Map<String, String>> paramsList = new ArrayList<>();
        List<List<BarCodeDto>> barCodesList = new ArrayList<>();
        ArrayList<String> qrCodeList = new ArrayList<>();

        // 模板地址
        String URL = "https://nbexpress.oss-us-east-1.aliyuncs.com/tmsFile/20250424_grid_label.pdf";

        for (TmsSortingGridEntity grid : gridList) {
            // 根据仓库id查询仓库名称
            String belongWarehouse = remoteTmsService.getWarehouseName(grid.getWarehouseId());

            // 面单参数
            Map<String, String> params = new HashMap<>();
            params.put("belong_warehouse", belongWarehouse);
            params.put("label_no", grid.getGridName());

            paramsList.add(params);           // 添加到列表
            templates.add(URL);               // 每一条都使用同一模板

            // 条码参数
            BarCodeDto barPara1 = new BarCodeDto(grid.getGridCode(), false, 1.2f, 40, 20, 1000);
            barPara1.setFontSize(12f);
            barPara1.setBaseLine(16f);
            barCodesList.add(Collections.singletonList(barPara1));

            // 二维码
            try {
                String base64QRCode = QRCodeGenerator.generateQRCodeBase64(grid.getGridCode(), 1, 1);
                qrCodeList.add(base64QRCode);
            } catch (Exception e) {
                qrCodeList.add("");  // 防止个别失败导致 list 不对齐
                System.err.println("二维码生成失败: " + e.getMessage());
            }
        }

        try {
            // 设置响应类型为 PDF
            response.setContentType("application/pdf");

            // 调用批量合并方法：一次性输出多页 PDF
            PdfUtilsToMany.mergeAndOutputPdf(
                    templates,
                    paramsList,
                    barCodesList,
                    null,
                    null,
                    response
            );
        } catch (Exception e) {
            System.err.println("PDF生成失败: " + e.getMessage());
        }

    }

    //打印卡派PUD
    @Override
    public void printKpPud(String trackNo, HttpServletResponse response) {
        if (StringUtils.isBlank(trackNo)) {
            return;
        }
        //获取订单相关信息
        TmsCustomerOrderEntity customerOrder = remoteTmsService.getCustomerOrder(trackNo, false);
        if (customerOrder == null) {
            return;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        //设置面单填充参数
        String origin = customerOrder.getOrigin();
        String[] splitOrigin = origin.split("/");
        String destination = customerOrder.getDestination();
        String[] splitDestination = destination.split("/");
        Map<String, String> params = new HashMap<String, String>();
        params.put("cus_no", customerOrder.getCustomerOrderNumber().contains("KH") ? "" : customerOrder.getCustomerOrderNumber());
        params.put("date", OrderTools.getFormattedDateMinusDays("yyyy-MM-dd", 0));
        params.put("d_date_start", customerOrder.getEstimatedShippingTimeStart() == null ?
                null : customerOrder.getEstimatedShippingTimeStart().format(formatter));
        params.put("d_date_end", customerOrder.getEstimatedShippingTimeEnd() == null ?
                null : customerOrder.getEstimatedShippingTimeEnd().format(formatter));
        params.put("type", customerOrder.getTransportType() == 1 ? "LTL" : "FTL");
        params.put("s_name", customerOrder.getShipperName());
        params.put("s_city", splitOrigin[1]);
        params.put("s_provide", splitOrigin[2]);
        params.put("s_address", customerOrder.getShipperAddress());
        params.put("s_zip", customerOrder.getShipperPostalCode());
        params.put("s_mobile", customerOrder.getShipperPhone());
        params.put("s_name", customerOrder.getShipperName());
        params.put("s_operate", customerOrder.getOperator());

        //发货人信息
        params.put("t_name", customerOrder.getReceiverName());
        params.put("t_city", splitDestination[1]);
        params.put("t_provide", splitDestination[2]);
        params.put("t_address", customerOrder.getDestAddress());
        params.put("t_zip", customerOrder.getDestPostalCode());
        params.put("t_mobile", customerOrder.getReceiverPhone());
        params.put("r_date_start", customerOrder.getEstimatedArrivalTimeStart() == null ?
                null : customerOrder.getEstimatedArrivalTimeStart().format(formatter));
        params.put("r_date_end", customerOrder.getEstimatedArrivalTimeEnd() == null ?
                null : customerOrder.getEstimatedArrivalTimeEnd().format(formatter));
        params.put("remark", customerOrder.getRemark());

        //获取货物信息
        List<TmsCargoInfoEntity> cargoInfoEntityList = customerOrder.getCargoInfoEntityList();
        BigDecimal totalWeight = BigDecimal.ZERO;
        BigDecimal totalVolume = BigDecimal.ZERO;
        int max = Math.min(cargoInfoEntityList.size(), 7);
        for (int i = 0; i < max; i++) {
            TmsCargoInfoEntity entity = cargoInfoEntityList.get(i);

            String length = OrderTools.formatDecimal(entity.getLength(), 2);
            String width = OrderTools.formatDecimal(entity.getWidth(), 2);
            String height = OrderTools.formatDecimal(entity.getHeight(), 2);
            String weight = OrderTools.formatDecimal(entity.getWeight(), 2);

            params.put("bagNum" + (i + 1), entity.getBagNum());
            params.put("cargoDescription" + (i + 1), entity.getCargoDescription());
            params.put("vo" + (i + 1), length + "*" + width + "*" + height);
            params.put("weight" + (i + 1), weight);
            totalWeight = totalWeight.add(entity.getWeight());
            totalVolume = totalVolume.add(entity.getLength().multiply(entity.getWidth()).multiply(entity.getHeight()));
        }


        params.put("totalWeight", OrderTools.formatDecimal(totalWeight, 2));
        params.put("totalVolume", OrderTools.formatDecimal(totalVolume, 2));
        params.put("totalQuantity", String.valueOf(max));


        //获取附加服务
        List<TmsAdditionalServicesEntity> additionalServicesEntityList = customerOrder.getAdditionalServicesEntityList();
        StringBuilder stringBuilder = new StringBuilder();
        additionalServicesEntityList.stream()
                .map(entity -> {
                    Object value = entity.getAdditionalServiceValue();
                    if (value == null) value = entity.getAdditionalServiceTime();
                    if (value == null) value = entity.getIsUrban();
                    return new AbstractMap.SimpleEntry<>(entity, value);
                })
                .filter(entry -> entry.getValue() != null)
                .forEach(entry -> {
                    System.out.println("值: " + entry.getValue());
                    stringBuilder.append(getDictByValue(entry.getKey().getAdditionalServiceType())).append("/");
                });

        if (stringBuilder != null && stringBuilder.length() > 0) {
            int len = stringBuilder.length();
            // 从末尾开始逐个检查斜杠并删除
            while (len > 0 && stringBuilder.charAt(len - 1) == '/') {
                stringBuilder.setLength(len - 1);
                len--;
            }
        }
        //附加服务
        params.put("add1", stringBuilder != null ? stringBuilder.toString() : "");


        String projectRoot = System.getProperty("user.dir");
        response.setContentType("application/pdf");
        //设置PDF参数
        BarCodeDto dto1 = new BarCodeDto(customerOrder.getEntrustedOrderNumber(), false, 0.9f, 50, 400, 743);
        dto1.setFontSize(14f);
        dto1.setBaseLine(25f);
        List<BarCodeDto> barCodes = Lists.newArrayList(dto1);

        String osName = System.getProperty("os.name").toLowerCase();
        String URL = "https://oss.jiayouexp.com//document/excel/upload/POD3.pdf";
        if (osName.contains("win")) {
            URL = projectRoot + File.separator + "jynx-basic" + File.separator + "jynx-basic-biz" + File.separator + "tpl" + File.separator + "POD3.pdf";
        }
        PdfUtils.toResponseStream(URL, params, barCodes, response);

    }


    //获取中大件派送PUD信息
    @Override
    public R getPud(String trackingNo, String apiKey) {
        TmsCustomerEntity tmsCustomerByToken = remoteTmsService.getTmsCustomerByToken(apiKey);
        if (tmsCustomerByToken == null) {
            return R.failed("apiKey无效");
        }
        PodVo customerOrderPod = remoteTmsService.getCustomerOrderPod(trackingNo);
        if (customerOrderPod== null || StrUtil.isBlank(customerOrderPod.getPod())) {
            return R.failed("POD不存在!");
        }
        //构造返回数据
        DeliveryProofVo vo = new DeliveryProofVo();
        String[] proofs = customerOrderPod.getPod().split(",");
        List<String> list = Arrays.asList(proofs);
        if (list.isEmpty()) {
            return R.failed("该订单暂无PUD信息!");

        }
        vo.setImageUrl(list);
        if (CollUtil.isNotEmpty(list)) {
            vo.setSignTime(customerOrderPod.getTime()==null ? "" : customerOrderPod.getTime().toString());
        }
        vo.setServiceTel("");
        vo.setSignAddress(customerOrderPod.getSignAddress());
        setLatLngFromReceiver(customerOrderPod.getReceiverLatLng(), vo);
        return R.ok(vo);
    }

    //中大件换单
    @Override
    public R exchangeOrder(ExchangeVo exchangeVo, String apiKey) {
        TmsCustomerEntity tmsCustomerByToken = remoteTmsService.getTmsCustomerByToken(apiKey);
        if (tmsCustomerByToken == null) {
            return R.failed("apiKey无效");
        }
        if (exchangeVo == null || StringUtils.isBlank(exchangeVo.getOldOrderNo()) || StringUtils.isBlank(exchangeVo.getNewOrderNo()) || StringUtils.isBlank(exchangeVo.getLabelUrl())) {
            return R.failed("所有参数不能为空");
        }
        exchangeVo.setChannel(tmsCustomerByToken.getCustomerName());
        return remoteTmsService.exchangeOrder(exchangeVo);
    }

    @Override
    public void printPickUpCode(List<Long> ids, HttpServletResponse response) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        Map<String, List<Long>> stringListHashMap = new HashMap<>();
        stringListHashMap.put("ids", ids);

        // 获取订单相关信息
        List<TmsZdjPickupEntity> pickupList = remoteTmsService.getPickUpCodeByIdForRomote(stringListHashMap);
//        TmsZdjPickupEntity tmsZdjPickup1 = new TmsZdjPickupEntity();
//        tmsZdjPickup1.setPickupCode("A-123-e2");
//        tmsZdjPickup1.setOrderNo("N2503D0000206");
//
//        TmsZdjPickupEntity tmsZdjPickup2 = new TmsZdjPickupEntity();
//        tmsZdjPickup2.setPickupCode("B-1a3-e2");
//        tmsZdjPickup2.setOrderNo("N2503D0000207");
//        List<TmsZdjPickupEntity> pickupList = new ArrayList<>();
//        pickupList.add(tmsZdjPickup1);
//        pickupList.add(tmsZdjPickup2);
        if (CollectionUtil.isEmpty(pickupList)) {
            return;
        }
        // 初始化需要批量打印的参数集合
        ArrayList<String> templates = new ArrayList<>();
        List<Map<String, String>> paramsList = new ArrayList<>();
        List<List<BarCodeDto>> barCodesList = new ArrayList<>();
        ArrayList<String> qrCodeList = new ArrayList<>();

        // 模板地址
//        String URL = "https://nbexpress.oss-us-east-1.aliyuncs.com/tmsFile/20250421_warehouse_label.pdf";
        String URL = "https://nbexpress.oss-us-east-1.aliyuncs.com/tmsFile/20250617_Pick_up_code.pdf";

        for (TmsZdjPickupEntity tmsZdjPickup : pickupList) {

            // 面单参数
            Map<String, String> params = new HashMap<>();
            params.put("pickupCode", tmsZdjPickup.getPickupCode());
            params.put("orderNo", tmsZdjPickup.getOrderNo());

            paramsList.add(params);           // 添加到列表
            templates.add(URL);               // 每一条都使用同一模板

            // 条码参数
            BarCodeDto barPara1 = new BarCodeDto(tmsZdjPickup.getPickupCode(), false, 1.2f, 40, 260, 0);
            //BarCodeDto barPara1 = new BarCodeDto(tmsLabelEntity.getLabelCode(), false, 1.2f, 40, 20, 150);
            barPara1.setFontSize(12f);
            barPara1.setBaseLine(16f);
            barCodesList.add(Collections.singletonList(barPara1));

            // 二维码
            try {
                String base64QRCode = QRCodeGenerator.generateQRCodeBase64(tmsZdjPickup.getPickupCode(), 1, 1);
                qrCodeList.add(base64QRCode);
            } catch (Exception e) {
                qrCodeList.add("");  // 防止个别失败导致 list 不对齐
                System.err.println("二维码生成失败: " + e.getMessage());
            }
        }

        try {
            // 设置响应类型为 PDF
            response.setContentType("application/pdf");

            // 调用批量合并方法：一次性输出多页 PDF
            PdfUtilsToMany.mergeAndOutputPdf(
                    templates,
                    paramsList,
                    barCodesList,
                    qrCodeList,
                    new QrCodePara(260L, 50L, 120L, 120L),
                    //new QrCodePara(100L, 230L, 95L, 95L),
                    response
            );
        } catch (Exception e) {
            System.err.println("PDF生成失败: " + e.getMessage());
        }
    }

    @Override
    public void payResultHandel(PaymentCallbackRequest request) {
        recordOrderData("PAY",request.toString());
    }


    /**
     * 取消订单
     * @param logiNo
     * @param token
     * @return
     */
    @Override
    public JSONObject cancel(String logiNo, String token) {
        if (StringUtils.isBlank(token)){
            return renderErr("500", "token不能为空");
        }
        ApiAuthEntity auth = apiAuthService.getAuthByToken(token);
        if (StringUtils.isBlank(logiNo)) {
            return renderErr("700099", "订单不能为空");
        }
        // 使用 QueryWrapper 构建查询条件
        LambdaQueryWrapper<SheinCodeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SheinCodeEntity::getSheinCode, logiNo).eq(SheinCodeEntity::getAuthId, auth.getId()).last("limit 1");
        SheinCodeEntity sheinCodeEntity = sheinCodeMapper.selectOne(queryWrapper);
        //调用接口取消订单
        if (sheinCodeEntity != null) {
            if (sheinCodeEntity.getStatus() > 1) {
                return renderErr("700091", "订单已存在路由信息，无法删除");
            }
            if (sheinCodeEntity.getIsUps()==YesOrNoConstants.YES){
                JSONObject result = upsService.voidShipment(logiNo);
                if (!"200".equals(result.get("code"))){
                    return renderErr("500", result.getString("errmsg"));
                }
            }
            SheinCodeEntity updateEntity = new SheinCodeEntity();
            updateEntity.setId(sheinCodeEntity.getId());
            updateEntity.setSheinCode(sheinCodeEntity.getSheinCode() + "_del");
            updateEntity.setIsDelete(1);
            updateEntity.setDeleteTime(LocalDateTime.now());
            sheinCodeMapper.updateById(updateEntity);
            return renderErr("0", "ok");
        }
        return renderErr("700090", "订单不存在");
    }

    //设置PUD经纬度
    private void setLatLngFromReceiver(String receiverLatLng , DeliveryProofVo vo) {
        if (receiverLatLng == null || receiverLatLng.isEmpty()) return;
        String[] latLng = receiverLatLng.split(",");
        if (latLng.length == 2) {
            vo.setLatitude(latLng[0]);
            vo.setLongitude(latLng[1]);
        }
    }


    //获取字典
    private String getDictByValue(String value) {
        R<List<SysDictItem>> dictByType = remoteUpmsService.getDictByType(DictConvertConstants.ADDITIONAL_SERVICE_TYPE, SecurityConstants.FROM_IN);
        List<SysDictItem> dict = dictByType.getData();
        for (SysDictItem sysDictItem : dict) {
            if (sysDictItem.getItemValue().equals(value)) {
                return sysDictItem.getRemarks();
            }
        }
        return "";
    }


    //退回客户仓库
    @Override
    public R returnCustomer(SheinPageBo sheinPageBo) {
        String ids = sheinPageBo.getIds();
        if (StrUtil.isNotBlank(ids)) {
            List<Long> idList = Arrays.stream(ids.split(";"))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            return updateStatus(idList, sheinPageBo.getReceiptImg()) ? R.ok() : R.failed();
        }
        String createDate = sheinPageBo.getIsCreateDate();
        String postInTime = sheinPageBo.getIsPostInTime();
        String driverTime = sheinPageBo.getIsDriverTime();
        String warehouseTime = sheinPageBo.getIsWarehouseTime();
        String returnCustomerTime = sheinPageBo.getIsReturnCustomerTime();
        if (warehouseTime == null) {
            return LocalizedR.failed("warehousing.time.must.be.selected", Optional.ofNullable(null));
        }
        if (OrderTools.getIntervalDays(warehouseTime) > 7) {
            return LocalizedR.failed("warehousing.time.difference.cannot.exceed.seven.days", Optional.ofNullable(null));
        }
        Integer status = sheinPageBo.getStatus();
        if (status != null && status != OrderConstants.STATUS_4_STORAGE) {
            return LocalizedR.failed("the.return.status.must.be.selected", Optional.ofNullable(null));
        }

        MPJLambdaWrapper<SheinCodeEntity> wrapper = new MPJLambdaWrapper<SheinCodeEntity>()
                .select(SheinCodeEntity::getId)
                .eq(ObjectUtil.isNotNull(sheinPageBo.getAuthId()), SheinCodeEntity::getAuthId, sheinPageBo.getAuthId())
                .eq(ObjectUtil.isNotNull(sheinPageBo.getSubChannel()), SheinCodeEntity::getSubChannel, sheinPageBo.getSubChannel())
                .eq(ObjectUtil.isNotNull(sheinPageBo.getSheinCode()), SheinCodeEntity::getSheinCode, sheinPageBo.getSheinCode())
                .eq(ObjectUtil.isNotNull(sheinPageBo.getCustomNo()), SheinCodeEntity::getCustomNo, sheinPageBo.getCustomNo())
                .eq(ObjectUtil.isNotNull(sheinPageBo.getUpsWaybillNumber()), SheinCodeEntity::getUpsWaybillNumber, sheinPageBo.getUpsWaybillNumber())
                .eq(ObjectUtil.isNotNull(sheinPageBo.getStatus()), SheinCodeEntity::getStatus, sheinPageBo.getStatus())
                .eq(ObjectUtil.isNotNull(sheinPageBo.getPostId()), SheinCodeEntity::getPostId, sheinPageBo.getPostId())
                .eq(ObjectUtil.isNotNull(sheinPageBo.getDriverId()), SheinCodeEntity::getDriverId, sheinPageBo.getDriverId())
                .eq(ObjectUtil.isNotNull(sheinPageBo.getWarehouseId()), SheinCodeEntity::getWarehouseId, sheinPageBo.getWarehouseId())
                .eq(ObjectUtil.isNotNull(sheinPageBo.getIsUps()), SheinCodeEntity::getIsUps, sheinPageBo.getIsUps())
                .eq(StrUtil.isNotBlank(sheinPageBo.getConsignee()), SheinCodeEntity::getConsignee, sheinPageBo.getConsignee())
                .eq(StrUtil.isNotBlank(sheinPageBo.getMobile()), SheinCodeEntity::getMobile, sheinPageBo.getMobile())
                .eq(StrUtil.isNotBlank(sheinPageBo.getSenderName()), SheinCodeEntity::getSenderName, sheinPageBo.getSenderName())
                .eq(StrUtil.isNotBlank(sheinPageBo.getSenderMobile()), SheinCodeEntity::getSenderMobile, sheinPageBo.getSenderMobile());
        if (createDate != null) {
            int splitIndex = createDate.indexOf("-");
            String startcreateDateTime = createDate.substring(0, splitIndex) + " 00:00:00";

            String endcreateDateTime = createDate.substring(splitIndex + 1) + " 23:59:59";
            wrapper.ge(SheinCodeEntity::getCreateDate, startcreateDateTime).le(SheinCodeEntity::getCreateDate, endcreateDateTime);
        }
        if (postInTime != null) {
            int splitIndex = postInTime.indexOf("-");
            String startcreateDateTime = postInTime.substring(0, splitIndex) + " 00:00:00";

            String endcreateDateTime = postInTime.substring(splitIndex + 1) + " 23:59:59";
            wrapper.ge(SheinCodeEntity::getPostInTime, startcreateDateTime).le(SheinCodeEntity::getPostInTime, endcreateDateTime);
        }
        if (driverTime != null) {
            int splitIndex = driverTime.indexOf("-");
            String startcreateDateTime = driverTime.substring(0, splitIndex) + " 00:00:00";

            String endcreateDateTime = driverTime.substring(splitIndex + 1) + " 23:59:59";
            wrapper.ge(SheinCodeEntity::getDriverTime, startcreateDateTime).le(SheinCodeEntity::getDriverTime, endcreateDateTime);
        }
        if (warehouseTime != null) {
            int splitIndex = warehouseTime.indexOf("-");
            String startcreateDateTime = warehouseTime.substring(0, splitIndex) + " 00:00:00";
            String endcreateDateTime = warehouseTime.substring(splitIndex + 1) + " 23:59:59";
            wrapper.ge(SheinCodeEntity::getWarehouseTime, startcreateDateTime).le(SheinCodeEntity::getWarehouseTime, endcreateDateTime);
        }
        if (returnCustomerTime != null) {
            int splitIndex = returnCustomerTime.indexOf("-");
            String startcreateDateTime = returnCustomerTime.substring(0, splitIndex) + " 00:00:00";
            String endcreateDateTime = returnCustomerTime.substring(splitIndex + 1) + " 23:59:59";
            wrapper.ge(SheinCodeEntity::getReturnCustomerTime, startcreateDateTime).le(SheinCodeEntity::getReturnCustomerTime, endcreateDateTime);
        }
        List<Long> idList = sheinCodeMapper.selectObjs(wrapper);
        if (idList.isEmpty()) {
            return LocalizedR.failed("no.eligible.orders.were.found", Optional.ofNullable(null));
        }
        return updateStatus(idList, sheinPageBo.getReceiptImg()) ? R.ok() : R.failed();
    }

    //查询经纬度
    @Override
    public String searchLocation(String postalCode) {
        if (StringUtils.isBlank(postalCode)) {
            return "postalCode is empty";
        }
        String geocode = findResult(postalCode);
        String api;
        String ret;
        if (StringUtils.isBlank(geocode)) {
            try {
                api = String.format(TkzjConstants.GEOCODE_API, URLEncoder.encode(postalCode, "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                System.out.println("经纬度查询错误");
                throw new RuntimeException(e);
            }
            // 代理
//            RestTemplate restTemplate = createRestTemplateWithProxy();
//            ret = restTemplate.getForObject(api, String.class);
            ret = HttpKit.Get(api);
            JSONObject jo = JSON.parseObject(ret);
            JSONArray results = jo.getJSONArray("results");
            if (results.size() > 0) {
                gmapGeocodeService.addResult(postalCode, ret);
            }
        } else {
            ret = geocode;
        }
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        JsonElement jsonElement = JsonParser.parseString(ret);
        return gson.toJson(jsonElement);

    }

    public RestTemplate createRestTemplateWithProxy() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setProxy(new Proxy(Proxy.Type.SOCKS, new InetSocketAddress("127.0.0.1", 7897)));
        return new RestTemplate(factory);
    }


    // 根据经纬度获取地址
    @Override
    public String searchCity(double lat, double lng) {
        String geocode = findResult(String.format("%.3f,%.3f", lat, lng));
        String api;
        String ret;
        if (StringUtils.isBlank(geocode)) {
            api = String.format(TkzjConstants.GEOCODE_CITY_API, String.format("%.6f", lat), String.format("%.6f", lng));
            ret = HttpKit.Get(api);
            JSONObject jo = JSON.parseObject(ret);
            if (null != jo) {
                JSONArray results = jo.getJSONArray("results");
                if (results.size() > 0) {
                    gmapGeocodeService.addResult(String.format("%.3f,%.3f", lat, lng), ret);
                }
            }
        } else {
            ret = geocode;
        }
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        JsonElement jsonElement = JsonParser.parseString(ret);
        return gson.toJson(jsonElement);
    }

    @Override
    public String getfindResult(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return null;
        }
        String md5 = OrderTools.getMD5(keyword.trim());
        // 使用 QueryWrapper 构建查询条件
        QueryWrapper<GmapGeocodeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("keyword_md5", md5).last("limit 1");
        // 查询并返回结果
        GmapGeocodeEntity gmapGeocodeEntity = gmapGeocodeMapper.selectOne(queryWrapper);
        if (gmapGeocodeEntity != null) {
            UpdateWrapper<GmapGeocodeEntity> UpdateWrapper = new UpdateWrapper<>();
            UpdateWrapper.eq("id", gmapGeocodeEntity.getId());
            GmapGeocodeEntity entity = new GmapGeocodeEntity();
            //有效请求次数+1
            entity.setReqCount(gmapGeocodeEntity.getReqCount() + 1);
            gmapGeocodeMapper.update(entity, UpdateWrapper);
            String result = gmapGeocodeEntity.getResult();
            return gmapGeocodeEntity.getResult();
        }
        return null;
    }


    public boolean updateStatus(List<Long> idList, String img) {
        SheinCodeEntity order = new SheinCodeEntity();
        order.setStatus(OrderConstants.STATUS_10_RETURN_CUSTOMER);
        order.setReturnCustomerTime(LocalDateTime.now());
        order.setBatchNo(getLatestBatchNo());
        order.setReceiptImg(img);
        return this.update(order, Wrappers.<SheinCodeEntity>lambdaUpdate().in(SheinCodeEntity::getId, idList));
    }

    //获取批次号
    public Long getLatestBatchNo() {
        SheinCodeEntity entity = this.getOne(Wrappers.<SheinCodeEntity>lambdaQuery()
                .isNotNull(SheinCodeEntity::getBatchNo)
                .orderByDesc(SheinCodeEntity::getReturnCustomerTime)
                .last("limit 1"));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        int newBatchNo = Integer.parseInt(sdf.format(new Date()) + "01");
        Long batchNo = entity.getBatchNo();
        return batchNo == null ? newBatchNo : batchNo + 1;
    }


    //获取驿站出站数量
    public List<Map<String, Object>> countPostOut() {
        QueryWrapper<SheinCodeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("post_id", "COUNT(1) AS count")
                .gt("post_id", 0)
                .between("driver_time", OrderTools.getYesterdayStart(), OrderTools.getYesterdayEnd())
                .groupBy("post_id");
        // 返回查询结果
        return this.listMaps(queryWrapper);
    }


    //获取正常订单
    public List<SheinCodeEntity> postNormalOrder(Integer postId) {
        LocalDateTime currentDateMinusTwoDay = LocalDateTime.now().minusHours(48);
        LambdaQueryWrapper<SheinCodeEntity> normaWrapper = new LambdaQueryWrapper<>();
        normaWrapper.eq(SheinCodeEntity::getPostId, postId)
                .eq(SheinCodeEntity::getStatus, OrderConstants.STATUS_2_POST)
                .eq(SheinCodeEntity::getHangUp, YesOrNoConstants.NO)
                .ge(SheinCodeEntity::getPostInTime, currentDateMinusTwoDay)
                .select(SheinCodeEntity::getSheinCode, SheinCodeEntity::getPostInTime);
        return sheinCodeMapper.selectList(normaWrapper);

    }

    //获取超时订单(退件)
    public List<SheinCodeEntity> postDelayOrder(Integer postId) {
        LocalDateTime currentDateMinusTwoDay = LocalDateTime.now().minusHours(48);
        LambdaQueryWrapper<SheinCodeEntity> delayWrapper = new LambdaQueryWrapper<>();
        delayWrapper.eq(SheinCodeEntity::getPostId, postId)
                .eq(SheinCodeEntity::getStatus, OrderConstants.STATUS_2_POST)
                .eq(SheinCodeEntity::getHangUp, YesOrNoConstants.NO)
                .lt(SheinCodeEntity::getPostInTime, currentDateMinusTwoDay)
                .select(SheinCodeEntity::getSheinCode, SheinCodeEntity::getPostInTime);
        return sheinCodeMapper.selectList(delayWrapper);
    }


    //获取groupID
    public String getGroupName(Integer groupId) {
        LambdaQueryWrapper<PostGroupEntity> groupWrapper = new LambdaQueryWrapper<>();
        groupWrapper.eq(PostGroupEntity::getGroupId, groupId)
                .last("limit 1");
        PostGroupEntity postGroupEntity = postGroupMapper.selectOne(groupWrapper);
        String groupName = "";
        if (postGroupEntity != null) {
            groupName = postGroupEntity.getGroupName();
        }
        return groupName;
    }

    //创建优时派订单
    public void createYpsOrder() {
        LocalDateTime oneDayAgo = LocalDateTime.now().minusMinutes(60 * 24*3);
        LambdaQueryWrapper<SheinCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.gt(SheinCodeEntity::getCreateDate, oneDayAgo)
                .eq(SheinCodeEntity::getAuthId, AuthConstants.AUTH_YSP)
                .eq(SheinCodeEntity::getStatus, OrderConstants.STATUS_1_CREATE)
                .eq(SheinCodeEntity::getIsUps, YesOrNoConstants.NO)
                .eq(SheinCodeEntity::getPushFlag, false);
        List<SheinCodeEntity> orderEntities = sheinCodeMapper.selectList(wrapper);
        for (int i = 0; i < orderEntities.size(); i++) {
            //创建订单
            String result = YspKit.create(orderEntities.get(i));
            Boolean flag = "OK".equals(result);
            LambdaUpdateWrapper<SheinCodeEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(SheinCodeEntity::getSheinCode, orderEntities.get(i).getSheinCode())
                    .set(SheinCodeEntity::getPushFlag, flag);
            if (!flag) {
                updateWrapper.set(SheinCodeEntity::getNote, result);
            }
            sheinCodeMapper.update(updateWrapper);
        }
    }

    /**
     * 分配驿站编码
     */
    public PostEntity allocationPostCode(String zip, String country, String province, String city, String address) {
        String postCode = "";
        PostEntity record = null;
        // 无驿站编码，需分配
        String api;
        try {
            String geocode = findResult(zip);
            String ret;
            if (StringUtils.isBlank(geocode)) {
                api = String.format(TkzjConstants.GEOCODE_API, URLEncoder.encode(zip, "UTF-8"));
                ret = HttpKit.Get(api);
            } else {
                ret = geocode;
            }
            JSONObject jo = JSON.parseObject(ret);
            JSONArray results = jo.getJSONArray("results");

            if (results.isEmpty()) {
                // 提示无法定位坐标，再根据省市区来一次
                api = String.format(TkzjConstants.GEOCODE_API, URLEncoder.encode(country + " " + province + " " + city + " " + address, "UTF-8"));
                ret = HttpKit.Get(api);
                jo = JSON.parseObject(ret);
                results = jo.getJSONArray("results");

                if (results.isEmpty()) {
                    return null;
                }
            } else if (geocode == null) {
                gmapGeocodeService.addResult(zip, ret);
            }

            JSONObject latlng = jo.getJSONArray("results").getJSONObject(0).getJSONObject("geometry").getJSONObject("location");
            double lat = latlng.getDouble("lat");
            double lng = latlng.getDoubleValue("lng");
            List<PostEntity> postEntities = postMapper.selectNearestPost(lat, lng, 1);
            if (postEntities.isEmpty()) {
                return null;
            }
            return postEntities.get(0);


        } catch (UnsupportedEncodingException e) {
            System.out.println("驿站编码分配错误");

        }
        return record;
    }

    //删除订单
    @Override
    @Transactional
    public JSONObject deleteOrder(String logiNo, String token) {
        ApiAuthEntity auth = apiAuthService.getAuthByToken(token);
        if (StringUtils.isBlank(logiNo)) {
            return renderErr("700099", "订单不能为空");
        }
        // 使用 QueryWrapper 构建查询条件
        LambdaQueryWrapper<SheinCodeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SheinCodeEntity::getSheinCode, logiNo).eq(SheinCodeEntity::getAuthId, auth.getId()).last("limit 1");
        SheinCodeEntity sheinCodeEntity = sheinCodeMapper.selectOne(queryWrapper);
        if (sheinCodeEntity != null) {
            if (sheinCodeEntity.getStatus() > 1) {
                return renderErr("700091", "订单已存在路由信息，无法删除");
            }
            SheinCodeEntity updateEntity = new SheinCodeEntity();
            updateEntity.setId(sheinCodeEntity.getId());
            updateEntity.setSheinCode(sheinCodeEntity.getSheinCode() + "_del");
            updateEntity.setIsDelete(1);
            updateEntity.setDeleteTime(LocalDateTime.now());
            sheinCodeMapper.updateById(updateEntity);
            return renderErr("0", "ok");
        }
        return renderErr("700090", "订单不存在");

    }


    //查询谷歌是否已经请求过
    public String findResult(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return null;
        }
        String md5 = OrderTools.getMD5(keyword.trim());
        // 使用 QueryWrapper 构建查询条件
        QueryWrapper<GmapGeocodeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("keyword_md5", md5).last("limit 1");
        // 查询并返回结果
        GmapGeocodeEntity gmapGeocodeEntity = gmapGeocodeMapper.selectOne(queryWrapper);
        if (gmapGeocodeEntity != null) {
            UpdateWrapper<GmapGeocodeEntity> UpdateWrapper = new UpdateWrapper<>();
            UpdateWrapper.eq("id", gmapGeocodeEntity.getId());
            GmapGeocodeEntity entity = new GmapGeocodeEntity();
            //有效请求次数+1
            entity.setReqCount(gmapGeocodeEntity.getReqCount() + 1);
            gmapGeocodeMapper.update(entity, UpdateWrapper);
            String result = gmapGeocodeEntity.getResult();
            return gmapGeocodeEntity.getResult();
        }
        return null;
    }


    public String generalById(int id) {
        String orderNo = action(id);
        return orderNo;
    }

    //生成订单号
    public String action(int id) {
        synchronized (Object.class) {
            OrderNoGeneratorEntity oldGenerator = generatorService.getById(id);
            String startWord = oldGenerator.getStartWord();
            String stopWord = oldGenerator.getStopWord();
            Long sequence = oldGenerator.getSequence();
            int sequenceLength = oldGenerator.getSequenceLength();
            Long newSeq = sequence + 1;
            String orderNo = startWord + String.format("%0" + sequenceLength + "d", sequence) + stopWord;
            OrderNoGeneratorEntity newGenerator = new OrderNoGeneratorEntity();
            newGenerator.setGeneratorId(oldGenerator.getGeneratorId());
            newGenerator.setSequence(newSeq);
            generatorService.updateById(newGenerator);
            OrderNoRecordEntity orderNoRecordEntity = new OrderNoRecordEntity();
            orderNoRecordEntity.setGeneratorId(oldGenerator.getGeneratorId());
            orderNoRecordEntity.setCreateDate(LocalDateTime.now());
            orderNoRecordEntity.setOrderNo(orderNo);
            recordService.save(orderNoRecordEntity);
            return orderNo;

        }

    }

    //获取分页结果
    public IPage<SheinPageBo> getPage(SheinCodePageRequestVo requestVo) {
        Integer[] emptyArray = new Integer[0];
        // 转换为 Page 对象
        Page<SheinPageBo> page = new Page<>(requestVo.getCurrent(), requestVo.getSize());
        SheinPageBo sheinPageBo = requestVo.getRequestVo();
        MPJLambdaWrapper conditionWrapper = getConditionWrapper(sheinPageBo, emptyArray);
        return sheinCodeMapper.selectJoinPage(page, SheinPageBo.class, conditionWrapper);
    }


    //导出
    @Override
    public List<OrderExcelVo> getExcel(SheinCodeExportVo requestVo) {
        MPJLambdaWrapper conditionWrapper = getConditionWrapper(requestVo.getRequestVo(), requestVo.getIds());
        //Long num = sheinCodeMapper.selectCount(conditionWrapper);
        return sheinCodeMapper.selectJoinList(OrderExcelVo.class, conditionWrapper);
    }


    //分页条件
    private MPJLambdaWrapper getConditionWrapper(SheinPageBo sheinPageBo, Integer[] exportIds) {
        Integer exceptionType = sheinPageBo.getExceptionType();
        String createDate = sheinPageBo.getIsCreateDate();
        String postInTime = sheinPageBo.getIsPostInTime();
        String driverTime = sheinPageBo.getIsDriverTime();
        String warehouseTime = sheinPageBo.getIsWarehouseTime();
        String orderNo = sheinPageBo.getSheinCode();
        String returnCustomerTime = sheinPageBo.getIsReturnCustomerTime();
        String selectSql = "city1.en_name as country, city2.en_name as province , city3.en_name as city";
        String leftJoinSql = "tkzj_zt_city city1 ON t.country_id = city1.city_id " +
                "LEFT JOIN tkzj_zt_city city2 ON t.province_id = city2.city_id " +
                "LEFT JOIN tkzj_zt_city city3 ON t.city_id = city3.city_id ";

        LocalDateTime currentDateMinusTwoDay = LocalDateTime.now().minusHours(48);
        MPJLambdaWrapper<SheinCodeEntity> wrapper = new MPJLambdaWrapper<SheinCodeEntity>()
                .selectAll(SheinCodeEntity.class)
                .select(PostEntity::getPostName)
                .selectAs(PostEntity::getAddress, OrderExcelVo::getPostAddress)
                .selectAs(PostEntity::getZip, OrderExcelVo::getPostZip)
                .select(DriverEntity::getDriverName)
                .selectAs(PackageParamEntity::getHeight, OrderExcelVo::getMHeight)
                .selectAs(PackageParamEntity::getLength, OrderExcelVo::getMLength)
                .selectAs(PackageParamEntity::getWidth, OrderExcelVo::getMWidth)
                .selectAs(PackageParamEntity::getWeight, OrderExcelVo::getMWeight)
                .select(selectSql)
                .eq(ObjectUtil.isNotNull(sheinPageBo.getAuthId()), SheinCodeEntity::getAuthId, sheinPageBo.getAuthId())
                .like(StringUtils.isNotBlank(sheinPageBo.getSubChannel()), SheinCodeEntity::getSubChannel, sheinPageBo.getSubChannel())
                .like(StringUtils.isNotBlank(sheinPageBo.getPostName()), SheinPageBo::getPostName, sheinPageBo.getPostName())
                .eq(StringUtils.isNotBlank(sheinPageBo.getCustomNo()), SheinCodeEntity::getCustomNo, sheinPageBo.getCustomNo())
                .eq(StringUtils.isNotBlank(sheinPageBo.getUpsWaybillNumber()), SheinCodeEntity::getUpsWaybillNumber, sheinPageBo.getUpsWaybillNumber())
                .eq(ObjectUtil.isNotNull(sheinPageBo.getStatus()), SheinCodeEntity::getStatus, sheinPageBo.getStatus())
                .eq(ObjectUtil.isNotNull(sheinPageBo.getPostId()), SheinCodeEntity::getPostId, sheinPageBo.getPostId())
                .eq(ObjectUtil.isNotNull(sheinPageBo.getDriverId()), SheinCodeEntity::getDriverId, sheinPageBo.getDriverId())
                .eq(ObjectUtil.isNotNull(sheinPageBo.getWarehouseId()), SheinCodeEntity::getWarehouseId, sheinPageBo.getWarehouseId())
                .eq(ObjectUtil.isNotNull(sheinPageBo.getIsUps()), SheinCodeEntity::getIsUps, sheinPageBo.getIsUps())
                .like(StringUtils.isNotBlank(sheinPageBo.getConsignee()), SheinCodeEntity::getConsignee, sheinPageBo.getConsignee())
                .eq(StringUtils.isNotBlank(sheinPageBo.getMobile()), SheinCodeEntity::getMobile, sheinPageBo.getMobile())
                .like(StringUtils.isNotBlank(sheinPageBo.getSenderName()), SheinCodeEntity::getSenderName, sheinPageBo.getSenderName())
                .eq(StringUtils.isNotBlank(sheinPageBo.getSenderMobile()), SheinCodeEntity::getSenderMobile, sheinPageBo.getSenderMobile())
                .eq(ObjectUtil.isNotNull(sheinPageBo.getIsDelete()), SheinCodeEntity::getIsDelete, sheinPageBo.getIsDelete())
                .eq(StringUtils.isNotBlank(sheinPageBo.getSourceCity()), SheinCodeEntity::getSourceCity, sheinPageBo.getSourceCity())
                .eq(ObjectUtil.isNotNull(sheinPageBo.getIsPudo()), PostEntity::getPudo, sheinPageBo.getIsPudo())
                .in(ObjectUtil.isNotNull(exportIds) && exportIds.length > 0, SheinCodeEntity::getId, exportIds)
                .leftJoin(PostEntity.class, PostEntity::getPostId, SheinCodeEntity::getPostId)
                .leftJoin(PackageParamEntity.class, PackageParamEntity::getOrderNumber, SheinCodeEntity::getSheinCode)
                .leftJoin(DriverEntity.class, DriverEntity::getDriverId, SheinCodeEntity::getDriverId)
                .leftJoin(leftJoinSql)
                .orderByDesc(SheinCodeEntity::getId);
        if (StringUtils.isNotBlank(orderNo) && orderNo.contains(";")) {
            wrapper.in(SheinCodeEntity::getSheinCode, orderNo.split(";"));
        } else if (StringUtils.isNotBlank(orderNo)) {
            wrapper.eq(StringUtils.isNotBlank(sheinPageBo.getSheinCode()), SheinCodeEntity::getSheinCode, sheinPageBo.getSheinCode())
                    .or().eq(StringUtils.isNotBlank(sheinPageBo.getSheinCode()), SheinCodeEntity::getSheinCode, sheinPageBo.getSheinCode() + "_del");
        }
        if ("1".equals(sheinPageBo.getIsReturnDelayPage())) {
            wrapper.eq(SheinCodeEntity::getStatus, OrderConstants.STATUS_2_POST)
                    .lt(SheinCodeEntity::getPostInTime, currentDateMinusTwoDay);
        }
        if (createDate != null) {
            // 将字符串转换为 LocalDateTime，开始时间设为 00:00:00，结束时间设为 23:59:59
            int splitIndex = createDate.indexOf("-");
            String startcreateDateTime = createDate.substring(0, splitIndex) + " 00:00:00";
            String endcreateDateTime = createDate.substring(splitIndex + 1) + " 23:59:59";
            wrapper.ge(SheinCodeEntity::getCreateDate, startcreateDateTime).le(SheinCodeEntity::getCreateDate, endcreateDateTime);
        }
        if (postInTime != null) {
            // 将字符串转换为 LocalDateTime，开始时间设为 00:00:00，结束时间设为 23:59:59
            int splitIndex = postInTime.indexOf("-");
            String startcreateDateTime = postInTime.substring(0, splitIndex) + " 00:00:00";
            String endcreateDateTime = postInTime.substring(splitIndex + 1) + " 23:59:59";
            wrapper.ge(SheinCodeEntity::getPostInTime, startcreateDateTime).le(SheinCodeEntity::getPostInTime, endcreateDateTime);
        }
        if (driverTime != null) {
            // 将字符串转换为 LocalDateTime，开始时间设为 00:00:00，结束时间设为 23:59:59
            int splitIndex = driverTime.indexOf("-");
            String startcreateDateTime = driverTime.substring(0, splitIndex) + " 00:00:00";
            String endcreateDateTime = driverTime.substring(splitIndex + 1) + " 23:59:59";
            wrapper.ge(SheinCodeEntity::getDriverTime, startcreateDateTime).le(SheinCodeEntity::getDriverTime, endcreateDateTime);
        }
        if (warehouseTime != null) {
            // 将字符串转换为 LocalDateTime，开始时间设为 00:00:00，结束时间设为 23:59:59
            int splitIndex = warehouseTime.indexOf("-");
            String startcreateDateTime = warehouseTime.substring(0, splitIndex) + " 00:00:00";
            String endcreateDateTime = warehouseTime.substring(splitIndex + 1) + " 23:59:59";
            wrapper.ge(SheinCodeEntity::getWarehouseTime, startcreateDateTime).le(SheinCodeEntity::getWarehouseTime, endcreateDateTime);
        }
        if (returnCustomerTime != null) {
            // 将字符串转换为 LocalDateTime，开始时间设为 00:00:00，结束时间设为 23:59:59
            int splitIndex = returnCustomerTime.indexOf("-");
            String startcreateDateTime = returnCustomerTime.substring(0, splitIndex) + " 00:00:00";
            String endcreateDateTime = returnCustomerTime.substring(splitIndex + 1) + " 23:59:59";
            wrapper.ge(SheinCodeEntity::getReturnCustomerTime, startcreateDateTime).le(SheinCodeEntity::getReturnCustomerTime, endcreateDateTime);
        }

        return wrapper;
    }


    //查询需要发送短信的订单
    @Override
    public List<ReturnSmsOrderVo> getOrdersSendSms() {
        LocalDateTime oneMinuteAgo = LocalDateTime.now().minus(Duration.ofMinutes(1));
        MPJLambdaWrapper<SheinCodeEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.select(SheinCodeEntity::getSheinCode, SheinCodeEntity::getPostInTime, SheinCodeEntity::getShipperPhone)
                .select(PostEntity::getPostName)
                .gt(SheinCodeEntity::getPostInTime, oneMinuteAgo)
                .leftJoin(PostEntity.class, PostEntity::getPostId, SheinCodeEntity::getPostId);
        return sheinCodeMapper.selectJoinList(ReturnSmsOrderVo.class, wrapper);
    }

    //查询优时派订单轨迹
    @Override
    public JSONObject trackYsp(String nbLogiNo) {
        SheinCodeEntity order = getOrderByOrderNoYSP(nbLogiNo);
        if (order == null) {
            JSONObject jo = new JSONObject();
            jo.put("code", 600100);
            jo.put("errmsg", "Order does not exist.");
            return jo;
        }
        if (1 == order.getIsUps()) {
            try {

                if (OrderTools.isBefore("2025-07-08 06:14:25",order.getCreateDate())){
                    String track = remoteTmsService.getTrack(order.getUpsWaybillNumber());
                    if (!"error".equals(track)){
                        System.out.println(track);
                        JSONObject jsonObject = UpsTrackingConverter.convertUpsRawToSimple(JSONObject.parseObject(track),order.getCustomNo(), nbLogiNo, order.getUpsWaybillNumber());
                        System.out.println(jsonObject);
                        return jsonObject;
                    }
                    JSONObject jo = new JSONObject();
                    jo.put("code", 500010);
                    jo.put("errmsg", "UPS Service is temporarily unavailable.");
                    return jo;
                }

                String result = VerykshipKit.getTracking(order.getUpsId());
                JSONObject jo = JSON.parseObject(result);
                if (jo.getIntValue("code") == 200) {
                    JSONObject json = new JSONObject();
                    JSONArray response = jo.getJSONArray("response");
                    JSONObject tracking = null;
                    try {
                        tracking = response.getJSONObject(0);
                    } catch (Exception e) {
                        jo.put("code", 500010);
                        jo.put("errmsg", "UPS track is null");
                        return jo;
                    }

                    JSONArray list = tracking.getJSONArray("list");
                    list.removeIf(item -> {
                        JSONObject subJo = (JSONObject) item;
                        return subJo.getString("context").contains("Thank you for using");
                    });
                    json.put("route", list);
                    json.put("nbLogiNo", order.getSheinCode());
                    json.put("logiNo", order.getThirdLogiNo());
                    json.put("isUps", true);
                    json.put("trackingUrl", tracking.getString("tracking_url"));
                    json.put("upsNo", order.getUpsWaybillNumber());
                    return json;
                } else {
                    jo.put("code", 500011);
                    jo.put("errmsg", jo.getString("message"));
                    return jo;
                }
            } catch (Exception e) {
                System.out.println("UPS 服务异常");
                JSONObject jo = new JSONObject();
                jo.put("code", 500010);
                jo.put("errmsg", "UPS Service is temporarily unavailable.");
                return jo;
            }

        } else {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            JSONObject json = new JSONObject();
            JSONArray ja = new JSONArray();
            int status = order.getStatus();
            if (status >= 4) {
                JSONObject jo = new JSONObject();
                jo.put("status", 4);
                if (order.getWarehouseTime().equals(order.getDriverTime())) {
                    Date date = Date.from(order.getWarehouseTime().atZone(ZoneId.systemDefault()).toInstant());
                    jo.put("time", DateFormatUtils.format(DateUtils.addMinutes(date, 2), "yyyy-MM-dd HH:mm:ss"));
                } else {
                    jo.put("time", order.getWarehouseTime()==null?"":order.getWarehouseTime().format(formatter));
                }
                jo.put("statusText", "Delivered");
                ja.add(jo);
            }
            if (status >= 3) {
                JSONObject jo = new JSONObject();
                jo.put("status", 3);
                jo.put("time", order.getDriverTime()==null?"":order.getDriverTime().format(formatter));
                jo.put("statusText", "In Transit");
                if (order.getDriverTime()!=null && order.getDriverTime().isBefore(order.getWarehouseTime())){
                ja.add(jo);
                }
            }
            if (status >= 2) {
                JSONObject jo = new JSONObject();
                jo.put("status", 2);
                jo.put("time", order.getPostInTime()==null?"":order.getPostInTime().format(formatter));
                jo.put("statusText", "Shipment received by NB Store");
                ja.add(jo);
            }
            if (status >= 1) {
                JSONObject jo = new JSONObject();
                jo.put("status", 1);
                jo.put("time", order.getCreateDate().format(formatter));
                jo.put("statusText", "Label Created");
                ja.add(jo);
            }
            json.put("route", ja);
            json.put("nbLogiNo", order.getSheinCode());
            json.put("logiNo", order.getThirdLogiNo());
            json.put("isUps", false);
            return json;
        }
    }



    //新增订单
    @Override
    public R saveOrder(OrderInsertBo orderInsertBo) {
        //查询单号是否重复

        SheinCodeEntity order = getOrderByOrderNo(orderInsertBo.getSheinCode());
        if (order != null) {
            return LocalizedR.failed("the.return.repeat.addition", Optional.ofNullable(orderInsertBo.getSheinCode()));
        }

        //生成订单号
        String orderNo = "";
        if (orderInsertBo.getAuthId() != AuthConstants.AUTH_SHEIN) {
            orderNo = generalById(orderInsertBo.getAuthId());
        }
        SheinCodeEntity orderEntity = new SheinCodeEntity();
        BeanUtils.copyProperties(orderInsertBo, orderEntity);
        if (StringUtils.isNotBlank(orderNo)) {
            orderEntity.setSheinCode(orderNo);
        }
        CityEntity country = cityService.findCacheById(orderInsertBo.getCountryId());
        CityEntity province = cityService.findCacheById(orderInsertBo.getProvinceId());
        CityEntity city = cityService.findCacheById(orderInsertBo.getCityId());
        orderEntity.setSenderCountryName(country.getEnName());
        orderEntity.setSenderProvinceName(province.getEnName());
        orderEntity.setSenderCityName(city.getEnName());
        orderEntity.setCreateDate(LocalDateTime.now());
        this.save(orderEntity);
        return LocalizedR.ok("the.return.status.successful", orderInsertBo.getCustomNo());
    }


    //获取获取司机取件数量
    @Override
    public List<SheinPageBo> getCountByDriverIdAndStatus(Integer driverId) {
        MPJLambdaWrapper<SheinCodeEntity> wrapper = new MPJLambdaWrapper<SheinCodeEntity>()
                .selectAll(SheinCodeEntity.class)
                .select(PostEntity::getPostName)
                .leftJoin(PostEntity.class, PostEntity::getPostId, SheinCodeEntity::getPostId)
                .eq(SheinCodeEntity::getDriverId, driverId)
                .eq(SheinCodeEntity::getStatus, OrderConstants.STATUS_3_DRIVER)
                .orderByDesc(SheinCodeEntity::getDriverTime);
        List<SheinPageBo> sheinPageBos = sheinCodeMapper.selectJoinList(SheinPageBo.class, wrapper);
        return sheinPageBos;
    }


    //获取未取件订单
    @Override
    public List<UnTakeOrderVo> getUnTakeOrder(Integer postId) {
        //查询是否是质检驿站
        List<UnTakeOrderVo> unTakeOrderVoList = new ArrayList<>();
        PostEntity post = postService.getById(postId);
        List<SheinCodeEntity> sheinCodeEntities = null;
        if (post.getIsWarehouse()) {
            QueryWrapper<SheinCodeEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("post_id", postId)
                    .orderByDesc("post_in_time");
            sheinCodeEntities = sheinCodeMapper.selectList(queryWrapper);
        } else {
            QueryWrapper<SheinCodeEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("post_id", postId)
                    .eq("status", OrderConstants.STATUS_2_POST)
                    .orderByDesc("post_in_time");
            sheinCodeEntities = sheinCodeMapper.selectList(queryWrapper);
        }
        for (SheinCodeEntity sheinCodeEntity : sheinCodeEntities) {
            UnTakeOrderVo unTakeOrderVo = new UnTakeOrderVo();
            BeanUtils.copyProperties(sheinCodeEntity, unTakeOrderVo);
            unTakeOrderVoList.add(unTakeOrderVo);
        }
        return unTakeOrderVoList;
    }

    @Override
    public SheinCodeEntity getOrder(String orderId) {
        QueryWrapper<SheinCodeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("shein_code", orderId);
        queryWrapper.last("limit 1");
        return sheinCodeMapper.selectOne(queryWrapper);
    }

    //包裹入站
    @Override
    public boolean receiveOrder(String orderNo, Integer postId, Integer employeeId) {
        if (StringUtils.isBlank(orderNo)) {
            return false;
        }
        PostEntity post = postService.getById(postId);
        //判断是否质检驿站
        SheinCodeEntity order = getOrderByOrderNo(orderNo);
        SheinCodeEntity sheinCodeEntity = new SheinCodeEntity();
        LambdaUpdateWrapper<SheinCodeEntity> updateWrapper = new LambdaUpdateWrapper<>();
        // 使用 orderNo 作为条件更新 sheinCodeEntity
        updateWrapper.eq(SheinCodeEntity::getSheinCode, orderNo);
        String consignee = order.getConsignee();
        if (post.getIsWarehouse()) {
            if ("SHEINCA".equals(consignee)) {
                return sheinCodeMapper.update(setNormalOrderEntity(sheinCodeEntity, postId, employeeId), updateWrapper) > 0;
            }
            if ("TEMU".equals(consignee)) {
                return sheinCodeMapper.update(setNormalOrderEntity(sheinCodeEntity, postId, employeeId), updateWrapper) > 0;
            }
            //优时派质检驿站入站流程
            if (consignee != null && consignee.contains("RTN-AE")) {
                return sheinCodeMapper.update(setYspOrderEntity(sheinCodeEntity, postId, employeeId), updateWrapper) > 0;
            }
            sheinCodeEntity.setStatus(OrderConstants.STATUS_3_DRIVER);
            sheinCodeEntity.setDriverId(post.getDefaultDriverId());
            sheinCodeEntity.setDriverTime(LocalDateTime.now());
            sheinCodeEntity.setPostInTime(LocalDateTime.now());
            sheinCodeEntity.setPostId(postId);
            sheinCodeEntity.setPostEmployeeId(employeeId);
            return sheinCodeMapper.update(sheinCodeEntity, updateWrapper) > 0;
        }
        return sheinCodeMapper.update(setNormalOrderEntity(sheinCodeEntity, postId, employeeId), updateWrapper) > 0;
    }

    //设置正常流程订单相关信息
    private SheinCodeEntity setNormalOrderEntity(SheinCodeEntity entity, Integer postId, Integer employeeId) {
        entity.setStatus(OrderConstants.STATUS_2_POST);
        entity.setPostInTime(LocalDateTime.now());
        entity.setPostId(postId);
        entity.setPostEmployeeId(employeeId);
        return entity;
    }

    //优时派质检驿站入站流程
    private SheinCodeEntity setYspOrderEntity(SheinCodeEntity entity, Integer postId, Integer employeeId) {
        entity.setStatus(OrderConstants.STATUS_4_STORAGE);
        entity.setPostInTime(LocalDateTime.now());
        entity.setPostId(postId);
        entity.setPostEmployeeId(employeeId);
        entity.setWarehouseTime(LocalDateTime.now());
        entity.setWarehouseId(postService.getById(postId).getWarehouseId());
        return entity;
    }


    public UniOrderEntity getUniOrderByOrderNo(String orderNo) {
        LambdaQueryWrapper<UniOrderEntity> query = new LambdaQueryWrapper<>();
        query.eq(UniOrderEntity::getYouniOrderNo, orderNo)
                .last("limit 1");
        return uniOrderService.getOne(query);
    }

    //驿站数据统计
    @Override
    public HashMap<String, Long> postStat(Integer postId) {
        //构建查询条件
        LocalDateTime startOfDay = LocalDateTime.now().with(LocalTime.MIN);
        LocalDateTime endOfDay = LocalDateTime.now().with(LocalTime.MAX);
        QueryWrapper<SheinCodeEntity> postInTimeWrapper = new QueryWrapper<>();
        postInTimeWrapper.eq("post_id", postId)
                .between("post_in_time", startOfDay, endOfDay);
        Long postInTimeCount = sheinCodeMapper.selectCount(postInTimeWrapper);
        QueryWrapper<SheinCodeEntity> driverTimeWrapper = new QueryWrapper<>();
        driverTimeWrapper.eq("post_id", postId)
                .between("driver_time", startOfDay, endOfDay);
        Long driverTimeCount = sheinCodeMapper.selectCount(driverTimeWrapper);
        HashMap<String, Long> countMap = new HashMap<>();
        countMap.put("postInTimeCount", postInTimeCount);
        countMap.put("driverTimeCount", driverTimeCount);
        return countMap;
    }


    //包裹分类
    @Override
    public IPage<SheinCodeEntity> orderSort(OrderSortBo orderSortBo, Page page) {
        Integer type = orderSortBo.getType();
        String orderNo = orderSortBo.getOrderNo();
        Integer postId = orderSortBo.getPostId();
        String startTime = orderSortBo.getStartTime();
        String endTime = orderSortBo.getEndTime();
        LocalDateTime currentDateMinusTwoDay = LocalDateTime.now().minusHours(48);
        QueryWrapper<SheinCodeEntity> wrapper = new QueryWrapper<>();
        switch (type) {
            //待取件
            case PostConstants.STATUS_1_POST:
                wrapper.eq("post_id", postId)
                        .eq("status", OrderConstants.STATUS_2_POST)
                        .eq(StringUtils.isNotBlank(orderNo), "shein_code", orderNo)
                        .ge(startTime != null, "post_in_time", startTime)
                        .le(endTime != null, "post_in_time", endTime)
                        .orderByDesc("post_in_time");
                break;
            //已取件
            case PostConstants.STATUS_2_DRIVER:
                wrapper.eq("post_id", postId)
                        .ge("status", OrderConstants.STATUS_3_DRIVER)
                        .eq(StringUtils.isNotBlank(orderNo), "shein_code", orderNo)
                        .ge(startTime != null, "post_in_time", startTime)
                        .le(endTime != null, "post_in_time", endTime)
                        .orderByDesc("driver_time");
                break;
            //超时
            case PostConstants.STATUS_3_TIMEOUT:
                wrapper.eq("post_id", postId)
                        .eq("status", OrderConstants.STATUS_2_POST)
                        .eq(StringUtils.isNotBlank(orderNo), "shein_code", orderNo)
                        .lt("post_in_time", currentDateMinusTwoDay)
                        .ge(startTime != null, "post_in_time", startTime)
                        .le(endTime != null, "post_in_time", endTime)
                        .orderByDesc("post_in_time");
                break;
            default:
                wrapper.eq("post_id", postId)
                        .eq(StringUtils.isNotBlank(orderNo), "shein_code", orderNo)
                        .orderByDesc("post_in_time");
                break;
        }
        return sheinCodeMapper.selectPage(page, wrapper);
    }

    public JSONObject renderErr(String code, String errmsg) {
        JSONObject jo = new JSONObject();
        jo.put("code", code);
        jo.put("errmsg", errmsg);
        return jo;
    }

    public JSONObject renderOK(String code, String errmsg) {
        JSONObject jo = new JSONObject();
        jo.put("code", code);
        jo.put("errmsg", errmsg);
        return jo;
    }


    //获取驿站单号（驿站账单用）
    @Override
    public List<SheinCodeEntity> getWarehouseOrderByTime(LocalDateTime startTime, LocalDateTime endTime, Integer
            postId) {
        LambdaQueryWrapper<SheinCodeEntity> wrapper = new LambdaQueryWrapper<>();wrapper .eq(SheinCodeEntity::getPostId, postId);
        //查询字符串时间是否在指定时间之前
       if (OrderTools.isBefore("2025-05-31 23:59:59", startTime)){
        wrapper.between(SheinCodeEntity::getPostInTime, startTime, endTime);
        }else {
           wrapper.between(SheinCodeEntity::getWarehouseTime, startTime, endTime);
       }
        return sheinCodeMapper.selectList(wrapper);
    }

    //查询需要定时任务处理的返仓订单
    @Override
    public List<SheinCodeEntity> getOrderByJobCondition(LocalDateTime time) {
        LambdaQueryWrapper<SheinCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(SheinCodeEntity::getWarehouseTime, time)
                .like(SheinCodeEntity::getNote, "创建-返仓");
        return sheinCodeMapper.selectList(wrapper);
    }


    @Override
    public R processFile(MultipartFile file) {
        // 验证文件类型和空文件
        if (file == null || file.isEmpty() || !isExcelFile(file.getOriginalFilename())) {
            return LocalizedR.failed("order.upload.valid.file", Optional.ofNullable(null));
        }
        try (InputStream inputStream = file.getInputStream()) {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            //List<List<Object>> lines = reader.read(1, reader.getRowCount());
            List<List<Object>> lines = reader.read(1, reader.getRowCount()); // 跳过表头，确保从第二行开始读取
            // 验证Excel数据是否为空
            if (lines == null || lines.isEmpty()) {
                return LocalizedR.failed("order.upload.empty.data", Optional.ofNullable(null));
            }

            // 创建集合来记录所有问题的字段
            List<String> errorMessages = new ArrayList<>();
            List<OrderInsertBo> orderList = new ArrayList<>();
            for (int i = 0; i < lines.size(); i++) {
                List<Object> line = lines.get(i);
                log.info("Processing line {}: {}", i, line);

                // 记录每行错误集合
                List<String> lineErrors = new ArrayList<>();

                // 验证字段
                String sheinCode = line.get(0).toString().trim();
                String customNo = validateStringField(line, 1, "customer order number cannot be empty/客户单号不能为空", i + 1, lineErrors);
                validateCustomNoField(line, 1, "customer order number is repeated/客户单号重复", i + 1, lineErrors);
                // 收件人
                validateStringField(line, 2, "country cannot be empty/国家不能为空", i + 1, lineErrors);
                validateStringField(line, 3, "Provinces cannot be empty/省份不能为空", i + 1, lineErrors);
                validateStringField(line, 4, "The city cannot be empty/城市不能为空", i + 1, lineErrors);
                String address = validateStringField(line, 5, "address cannot be empty/地址不能为空", i + 1, lineErrors);
                String zip = validateStringField(line, 6, "postcode cannot be empty/邮编不能为空", i + 1, lineErrors);
                String destName = validateStringField(line, 7, "recipient cannot be empty/收件人不能为空", i + 1, lineErrors);
                String destPhone = validateStringField(line, 8, "recipient mobile phone number cannot be empty/收件人手机号不能为空", i + 1, lineErrors);
                BigDecimal weight = validateBigDecimalField(line, 9, "weight cannot be empty/重量不能为空", i + 1, lineErrors);
                BigDecimal price = validateBigDecimalField(line, 10, "price cannot be empty/价格不能为空", i + 1, lineErrors);
                String destEmail = validateStringField(line, 11, "recipient mailbox cannot be empty/收件人邮箱不能为空", i + 1, lineErrors);
                String authId = validateStringField(line, 12, "source channel cannot be empty/来源渠道不能为空", i + 1, lineErrors);
                // 发件人
                String senderName = validateStringField(line, 13, "sender cannot be empty/发件人不能为空", i + 1, lineErrors);
                String senderTel = validateStringField(line, 14, "sender phone cannot be empty/发件人电话不能为空", i + 1, lineErrors);
                validateStringField(line, 15, "sender country cannot be empty/发件人国不能为空", i + 1, lineErrors);
                validateStringField(line, 16, "Sender province cannot be empty/发件人省不能为空", i + 1, lineErrors);
                validateStringField(line, 17, "sender market cannot be empty/发件人市不能为空", i + 1, lineErrors);
                String senderAddress = validateStringField(line, 18, "sender address cannot be empty/发件人地址不能为空", i + 1, lineErrors);
                String senderPostalCode = validateStringField(line, 19, "sender zip code cannot be empty/发件人邮编不能为空", i + 1, lineErrors);
                // 包裹
                //BigDecimal packageWeight = validateBigDecimalField(line, 20, "Parcel weight cannot be empty/包裹重量不能为空", i + 1, lineErrors);
                BigDecimal packageLength = validateBigDecimalField(line, 20, "package length cannot be empty/包裹长不能为空", i + 1, lineErrors);
                BigDecimal packageWidth = validateBigDecimalField(line, 21, "package width cannot be empty/包裹宽不能为空", i + 1, lineErrors);
                BigDecimal packageHeight = validateBigDecimalField(line, 22, "package height cannot be empty/包裹高不能为空", i + 1, lineErrors);
                String subChannel = validateStringField(line, 23, "subchannel code cannot be empty/子渠道代码不能为空", i + 1, lineErrors);

                //省市区校验查询转换处理
                Integer country = validateCityField(line, 2, "country invalid", i + 1, "national", lineErrors);
                Integer province = validateCityField(line, 3, "province invalid", i + 1, "province", lineErrors);
                Integer city = validateCityField(line, 4, "city invalid", i + 1, "city", lineErrors);
                //发件人省市区校验查询转换处理
                Integer senderCcountryId = validateCityField(line, 15, "senderCountry invalid", i + 1, "national", lineErrors);
                Integer senderProvinceId = validateCityField(line, 16, "senderProvince invalid", i + 1, "province", lineErrors);
                Integer senderCityId = validateCityField(line, 17, "senderCity invalid", i + 1, "city", lineErrors);

                // 如果当前行有错误，跳过该行
                if (!lineErrors.isEmpty()) {
                    // 将当前行的所有错误添加到全局错误信息列表中
                    errorMessages.addAll(lineErrors);
                } else {
                    //准备插入字段阶段--------------------------------------
                    OrderInsertBo order = new OrderInsertBo();
                    order.setSheinCode(sheinCode);
                    order.setCustomNo(customNo);

                    //收件人信息
                    order.setCountryId(country);
                    order.setProvinceId(province);
                    order.setCityId(city);
                    order.setAddress(address);
                    order.setZip(zip);
                    order.setConsignee(destName);
                    order.setMobile(destPhone);
                    order.setWeight(weight);
                    order.setPrice(price);
                    order.setEmail(destEmail);
                    int authValue = 0;
                    switch (authId) {
                        case "希音":
                            authValue = AuthConstants.AUTH_SHEIN;
                            break;
                        case "优时派":
                            authValue = AuthConstants.AUTH_YSP;
                            break;
                        case "顺丰":
                            authValue = AuthConstants.AUTH_SF;
                            break;
                    }
                    order.setAuthId(authValue);
                    //发件人信息
                    order.setSenderName(senderName);
                    order.setSenderMobile(senderTel);
                    order.setSenderCountryId(senderCcountryId);
                    order.setProvinceId(senderProvinceId);
                    order.setSenderCityId(senderCityId);
                    order.setSenderAddress(senderAddress);
                    order.setSenderPostalcode(senderPostalCode);
                    //包裹信息
                    //order.setPackageWeight(packageWeight);
                    order.setPackageLength(packageLength);
                    order.setPackageWidth(packageWidth);
                    order.setPackageHeight(packageHeight);
                    order.setSubChannel(subChannel);
                    orderList.add(order);
                }


            }

            // 处理成功订单信息
            Integer orderCount = processOrders(orderList);
            //记录成功条数信息
            String successMessage = "Successfully processed: " + orderCount + " rows.";

            if (errorMessages.isEmpty()) {
                return LocalizedR.ok("order.delivery.note.Excel.file.processing.success", successMessage);
            } else {
                // 返回成功数量和错误信息
                return LocalizedR.failed("order.file.processing.errors", successMessage + "<br>Errors:<br>" + errorMessages);
            }
        } catch (IOException e) {
            log.error("退件单Excel文件处理异常", e);
            return LocalizedR.failed("order.delivery.note.Excel.file.processing.exception", e.getMessage());
        }
    }

    //接收小包订单状态
    @Override
    public JSONObject receiveOrderStatus(OrderStatusBo statusBo) {
        String trackTime = statusBo.getTrackTime();
        LocalDateTime date = null;
        try {
            // 自定义格式化器
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            // 将字符串转换为 LocalDateTime
            date = LocalDateTime.parse(trackTime, formatter);
        } catch (Exception e) {
            return renderErr("40063", "时间格式必须为yyyy-MM-dd HH:mm:ss");
        }
        //查询订单是否存在
        SheinCodeEntity sheinCode = sheinCodeMapper.selectOne(new LambdaQueryWrapper<SheinCodeEntity>().eq(SheinCodeEntity::getSheinCode, statusBo.getTrackNumber()));
        if (sheinCode == null) {
            return renderErr("40063", "订单不存在");
        }
        SheinCodeEntity orderEntity = new SheinCodeEntity();
        orderEntity.setId(sheinCode.getId());
        //查询驿站
        PostEntity postEntity = postService.getPostByPostNo(statusBo.getDealerNo());
        if (postEntity != null) {
            orderEntity.setPostId(postEntity.getPostId());
            orderEntity.setPostInfo("当前时区为" + statusBo.getTimeZone());
        } else {
            //非合作驿站
            orderEntity.setPostId(419);
            orderEntity.setPostInfo("非合作驿站入站-" + statusBo.getDealerNo() + "-" + statusBo.getDealerName() + "-" + statusBo.getDealerAddress() + "-" + statusBo.getDealerPhone());
        }
        orderEntity.setPostInTime(date);
        orderEntity.setPostEmployeeId(0);
        if (sheinCode.getStatus() == OrderConstants.STATUS_1_CREATE) {
            orderEntity.setStatus(OrderConstants.STATUS_2_POST);
        }
        if (sheinCode.getPostInTime() != null) {
            return renderErr("40063", "已存在入站时间");
        }
        if (!date.toLocalDate().equals(LocalDate.now())) {
            orderEntity.setNote("入站时间" + date);
        }
        //更新
        sheinCodeMapper.updateById(orderEntity);
        return renderOK("200", "ok");
    }

    //有你包裹入站
    @Override
    public R receiveUniOrder(String orderNo, Integer postId, Integer employeeId) {
        if (StringUtils.isEmpty(orderNo)) {
            return R.failed("orderNo is empty!");
        }
        //判断是否订单已经存在
        UniOrderEntity uniOrderEntity = uniOrderService.getUniOrderByOrderNo(orderNo);
        if (uniOrderEntity != null) {
            return R.failed("Repeat Scan!");
        }
        PostEntity post = postService.getById(postId);
        JSONObject jo = UniKit.request(post.getPostNo(), orderNo, UniKit.STORAGE_SERVICE_POINT, null);
        if (jo.getString("status").equals("SUCCESS")) {
            //订单存在，保存
            UniOrderEntity order = new UniOrderEntity();
            order.setYouniOrderNo(orderNo.trim());
            order.setPostId(postId);
            order.setPostEmployeeId(employeeId);
            order.setPostInTime(LocalDateTime.now());
            order.setStatus(UniOrderConstants.STATUS_1_IN_POST);
            uniOrderService.save(order);
            return R.ok("200", "SUCCESS!");
        }
        return R.failed("The orderNo does not exist.!");
    }

    //有你包裹签收
    @Override
    public R uniOrderSign(String orderNo, Integer postId, String signUrl) {
        UniOrderEntity order = uniOrderService.getUniOrderByOrderNo(orderNo);
        if (order == null) {
            return R.failed("The orderNo does not exist!");
        }
        if (order.getStatus() == UniOrderConstants.STATUS_2_TAKE) {
            return R.failed("repetitive sign");
        }

        URL url = null;
        try {
            url = new URL(signUrl);
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }

        // 目标保存路径
        //String saveDir = "D:\\data\\static\\unisign\\202502\\";

        String saveDir = "/data/static/unisign/202502/";
        System.out.println("获取文件名...");
        // 从 URL 中提取文件名
        String fileName = Paths.get(url.getPath()).getFileName().toString();

        if (fileName.isEmpty()) {
            System.out.println("无法从 URL 中提取文件名");

        }

        // 创建保存目录
        File directory = new File(saveDir);
        if (!directory.exists()) {
            if (directory.mkdirs()) {
                System.out.println("目录创建成功: " + saveDir);
            } else {
                System.out.println("目录创建失败: " + saveDir);
            }
        }

        // 创建目标文件
        File outputFile = new File(saveDir + fileName);

        try (InputStream inputStream = url.openStream();
             FileOutputStream outputStream = new FileOutputStream(outputFile)) {

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            System.out.println("文件下载成功，保存到: " + outputFile.getAbsolutePath());
        } catch (IOException e) {
            e.printStackTrace();
        }
        PostEntity post = postService.getById(postId);
        JSONObject jo = UniKit.request(post.getPostNo(), orderNo, UniKit.SELF_PICK_UP, outputFile);
        System.out.println("请求结果：" + jo.toJSONString());
        order.setOrderId(order.getOrderId());
        order.setSignFilePath(signUrl);
        order.setStatus(UniOrderConstants.STATUS_2_TAKE);
        order.setTakeTime(LocalDateTime.now());
        if (jo.containsKey("status") && jo.getString("status").equals("SUCCESS")) {
            uniOrderService.updateById(order);
            return R.ok("200", "SUCCESS!");
        } else {
            order.setNote("NB本地系统签收，UNI系统未签收");
            uniOrderService.updateById(order);
            return R.ok("200", "SUCCESS!");
        }

    }

    //驿站打印面单统计
    @Override
    public void printCount(String orderNo, Integer postId) {
        //查询订单是否存在
//        SheinCodeEntity sheinCode = getOrderByOrderNo(orderNo);

//        if (sheinCode == null) {
//            return R.failed("The orderNo does not exist!");
//        }
//        if (StringUtils.isBlank(labelUrl)) {
//            labelUrl = sheinCode.getLabelPath();
//        }
        //查询是否重复扫描
//        LambdaQueryWrapper<SheinCodeEntity> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(SheinCodeEntity::getSheinCode, orderNo)
//                .isNotNull(SheinCodeEntity::getPrintTime)
//                .last("limit 1");
//        SheinCodeEntity order = this.getOne(wrapper);
//        if (order != null) {
//            return R.failed("The orderNo has been scanned!");
//        }
        LambdaUpdateWrapper<SheinCodeEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SheinCodeEntity::getSheinCode, orderNo)
                .set(SheinCodeEntity::getPrintPost, postId)
                .set(SheinCodeEntity::getPrintTime, LocalDateTime.now());
        this.update(updateWrapper);
    }


    //获取打印记录
    @Override
    public R printList(Page page, Integer postId) {
        LambdaQueryWrapper<SheinCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(SheinCodeEntity::getSheinCode, SheinCodeEntity::getPrintTime, SheinCodeEntity::getLabelPath)
                .eq(SheinCodeEntity::getPrintPost, postId)
                .orderByDesc(SheinCodeEntity::getPrintTime);
        IPage<SheinCodeEntity> list = this.page(page, wrapper);
        return R.ok(list);
    }

    @Override
    public List<SheinCodeEntity> listRetuenSectionOrder() {

        MPJLambdaWrapper<SheinCodeEntity> wrapper = new MPJLambdaWrapper<SheinCodeEntity>()
                .select(SheinCodeEntity::getId)  // s.id
                .select(SheinCodeEntity::getSheinCode)  // s.shein_code
                .select(SheinCodeEntity::getAuthId)  // s.auth_id
                .select(SheinCodeEntity::getPackageWeight)  // s.package_weight
                .select(PackageParamEntity::getWeight)  // p.weight
                .select(PostEntity::getZip)  // po.zip
                .leftJoin(PackageParamEntity.class, PackageParamEntity::getOrderNumber, SheinCodeEntity::getSheinCode)  // 左连接tkzj_zt_packageparam
                .leftJoin(PostEntity.class, PostEntity::getPostId, SheinCodeEntity::getPostId)  // 左连接tkzj_zt_post
                .eq(SheinCodeEntity::getIsDelete, 0)  // s.is_delete = 0
                .eq(SheinCodeEntity::getStatus, 10)  // s.status = 10
                .eq(SheinCodeEntity::getAuthId, 1)     //只判断希音目前
                .isNull(SheinCodeEntity::getCalculateAmount)  // s.calculate_amount IS NULL
                .between(SheinCodeEntity::getReturnCustomerTime, "2025-01-01 00:00:00", "2025-02-05 23:59:59")  // s.return_customer_time 在时间范围内
                .eq(PostEntity::getIsValid, 1);  // po.is_valid = 1

        // 执行查询
        List<SheinCodeEntity> resultList = this.list(wrapper);
        return resultList;
    }


    //小包获取订单信息
    @Override
    public Object getOrderData(String startTime, String endTime, String type) {
        if (StringUtils.isBlank(startTime) | StringUtils.isBlank(endTime)) {
            return new OldResult("500", "时间范围不能为空");
        }
        if (StringUtils.isBlank(type)) {
            return new OldResult("500", "type不能为空");
        }
        MPJLambdaWrapper<SheinCodeEntity> wrapper = new MPJLambdaWrapper<SheinCodeEntity>()
                .selectAs(PostEmployeeEntity::getRealname, OrderVo::getPostEmployee)
                .selectAs(SheinCodeEntity::getSheinCode, OrderVo::getOrderNo)
                .selectAs(SheinCodeEntity::getSourceCity, OrderVo::getSourceCity)
                .selectAs(PostEntity::getPostName, OrderVo::getPostName)
                .selectAs(SheinCodeEntity::getPostInTime, OrderVo::getPostInTime)
                .selectAs(SheinCodeEntity::getWarehouseTime, OrderVo::getWareHouseTime)
                .selectAs(WarehouseEntity::getWarehouseName, OrderVo::getWareHouseName);
        if ("post".equals(type)) {
            wrapper.ge(SheinCodeEntity::getPostInTime, startTime)
                    .le(SheinCodeEntity::getPostInTime, endTime);
        } else {
            wrapper.ge(SheinCodeEntity::getWarehouseTime, startTime)
                    .le(SheinCodeEntity::getWarehouseTime, endTime);
        }
        wrapper.leftJoin(PostEntity.class, PostEntity::getPostId, SheinCodeEntity::getPostId)
                .leftJoin(PostEmployeeEntity.class, PostEmployeeEntity::getEmployeeId, SheinCodeEntity::getPostEmployeeId)
                .leftJoin(WarehouseEntity.class, WarehouseEntity::getWarehouseId, SheinCodeEntity::getWarehouseId)
                .orderByDesc(SheinCodeEntity::getCreateDate);
        List<OrderVo> orderVos = this.baseMapper.selectJoinList(OrderVo.class, wrapper);
        for (OrderVo orderVo : orderVos) {
            if (orderVo.getPostEmployee() == null) {
                SheinCodeEntity order = getOrderByOrderNo(orderVo.getOrderNo());
                Integer postEmployeeId = order.getPostEmployeeId();
                orderVo.setPostEmployee(postEmployeeId == null ? "0" : String.valueOf(postEmployeeId));
            }
        }
        return orderVos;
    }


    //处理批量导入订单新增
    @Transactional(rollbackFor = Exception.class)
    public Integer processOrders(List<OrderInsertBo> orderList) {
        Integer count = 0;
        for (OrderInsertBo order : orderList) {
            //生成订单号
            String orderNo = "";
            if (order.getAuthId() != AuthConstants.AUTH_SHEIN) {
                orderNo = generalById(order.getAuthId());
            }
            SheinCodeEntity orderEntity = new SheinCodeEntity();
            BeanUtils.copyProperties(order, orderEntity);
            if (StringUtils.isNotBlank(orderNo)) {
                orderEntity.setSheinCode(orderNo);
            }
            orderEntity.setCreateDate(LocalDateTime.now());
            boolean save = this.save(orderEntity);
            if (save) {
                count++;
            }
        }
        return count;
    }

    // 校验文件是否是.xls或.xlsx格式
    private boolean isExcelFile(String fileName) {
        return fileName != null && (fileName.endsWith(".xls") || fileName.endsWith(".xlsx"));
    }

    //校验Integer类型字段是否为空
    private Integer validateIntegerField(List<Object> line, int index, String errorMessage, int row, List<String> lineErrors) {
        Object value = line.get(index);
        if (value == null) {
            lineErrors.add("row" + row + "：" + errorMessage);
            return null;
        }
        try {
            return Integer.valueOf(value.toString());
        } catch (NumberFormatException e) {
            lineErrors.add("row" + row + "：" + value + "(format error)");
            return null;
        }
    }

    //校验String类型字段是否为空
    private String validateStringField(List<Object> line, int index, String errorMessage, int row, List<String> lineErrors) {
        Object value = line.get(index);
        if (value == null || value.toString().trim().isEmpty()) {
            lineErrors.add("row" + row + "：" + errorMessage);
            return null;
        }
        return value.toString().trim();
    }

    //校验Decimal类型字段是否为空
    private BigDecimal validateBigDecimalField(List<Object> line, int index, String errorMessage, int row, List<String> lineErrors) {
        Object value = line.get(index);
        if (value == null) {
            lineErrors.add("row" + row + "：" + errorMessage);
            return null;
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            lineErrors.add("row" + row + "：" + value + "(format error)");
            return null;
        }
    }

    //校验客户单号是否重复
    private String validateCustomNoField(List<Object> line, int index, String errorMessage, int row, List<String> lineErrors) {
        Object value = line.get(index);
        List<SheinCodeEntity> sheinCodeEntities = sheinCodeMapper.selectList(new LambdaQueryWrapper<SheinCodeEntity>().eq(SheinCodeEntity::getCustomNo, value.toString().trim()).last("limit 1"));
        if (CollUtil.isNotEmpty(sheinCodeEntities)) {
            lineErrors.add("row" + row + "：" + errorMessage);
            return null;
        }
        return value.toString().trim();
    }

    //校验省市区是否有效
    private Integer validateCityField(List<Object> line, int index, String errorMessage, int row, String locationType, List<String> lineErrors) {
        Object value = line.get(index);
        CityEntity cityEntity = null;

        switch (locationType) {
            case "national":
                cityEntity = cityService.selectCityEntity(0, value.toString().trim());
                break;
            case "province":
                cityEntity = cityService.selectCityEntity(1, value.toString().trim());
                break;
            case "city":
                cityEntity = cityService.selectCityEntity(2, value.toString().trim());
                break;
        }

        if (cityEntity == null) {
            lineErrors.add("row" + row + "：" + errorMessage);
            return null;
        }


        return cityEntity.getCityId(); // 假设CityEntity中有cityName字段，可以返回名称
    }

    /**
     * 获取六大城市列表
     *
     * @return
     */
    @Override
    public List<SheinCodeEntity> getCityList() {
        LambdaQueryWrapper<SheinCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(SheinCodeEntity::getSourceCity)
                .ne(SheinCodeEntity::getSourceCity, "Other")
                .isNotNull(SheinCodeEntity::getSourceCity)
                .groupBy(SheinCodeEntity::getSourceCity);
        return this.list(wrapper);
    }

    //PUD订单信息推送
    @Override
    public R pushPudOrderData(PudOrderBo pudOrderBo) {
        if (StringUtils.isBlank(pudOrderBo.getSenderPostalcode())) {
            return R.failed("Invalid PostalCode");
        }
        if (StringUtils.isBlank(pudOrderBo.getTrackNumber())) {
            return R.failed("Invalid trackNumber");
        }
        if (StringUtils.isBlank(pudOrderBo.getSenderAddress())) {
            return R.failed("Invalid senderAddress");
        }
        SheinCodeEntity order = this.getOrderByOrderNo(pudOrderBo.getTrackNumber());
        if (order != null) {
            return R.failed("Order already exists");
        }
        if ("JiaYouNb".equals(pudOrderBo.getKey())) {
            SheinCodeEntity entity = new SheinCodeEntity();
            entity.setSheinCode(pudOrderBo.getTrackNumber());
            entity.setSenderPostalcode(pudOrderBo.getSenderPostalcode());
            entity.setSenderAddress(pudOrderBo.getSenderAddress());
            entity.setAddress("test");
            entity.setCreateDate(LocalDateTime.now());
            entity.setStatus(OrderConstants.STATUS_1_CREATE);
            entity.setCustomNo("test");
            entity.setCountryId(1);
            entity.setProvinceId(1);
            entity.setCityId(1);
            entity.setZip("test");
            entity.setMobile("test");
            entity.setAuthId(10);
            entity.setConsignee("test");
            this.save(entity);
            return R.ok(null, "success");
        } else {
            return R.failed("Invalid Key");
        }

    }

    /**
     * 统计监控寄出-签收的百分比
     *
     * @param requestVo 参数对象
     * @param
     * @return
     */
    @Override
    public R listPickSign(SheinPickSignBo requestVo) {
        // 查询总订单数量
        MPJLambdaWrapper<SheinCodeEntity> wrapper = new MPJLambdaWrapper<SheinCodeEntity>();

        // 获取按日期、合作商、仓库分组的单日签收订单数量
        //"ROUND(COUNT(*) * 100.0 / " + totalOrderCount + ", 2) AS daily_receive_rate") // 直接在 SQL 计算
        MPJLambdaWrapper<SheinCodeEntity> wrapperDay = new MPJLambdaWrapper<SheinCodeEntity>()
                .select("FLOOR(TIMESTAMPDIFF(SECOND, post_in_time, return_customer_time) / 86400) + 1 AS receive_day",  // 按天数分组
                        "auth_id",  // 合作商ID
                        "warehouse_id",  // 仓库ID
                        "COUNT(*) AS orders_day",   // 每日签收订单数
                        "SUM(count(*)) OVER (PARTITION BY auth_id, warehouse_id ORDER BY FLOOR(TIMESTAMPDIFF(SECOND, post_in_time, return_customer_time) / 86400) + 1) AS orders_received" // 累计每日签收订单数
                );  // 只查询已签收的订单

        // 先根据时间区间查询
        if (null != requestVo.getIsWarehouseTime() && null != requestVo.getIsReturnCustomerTime()) {
            // 给这两个时间添加00:00:00 和 23:59:59
            requestVo.setIsWarehouseTime(requestVo.getIsWarehouseTime() + " 00:00:00");
            requestVo.setIsReturnCustomerTime(requestVo.getIsReturnCustomerTime() + " 23:59:59");
            wrapper.between(SheinCodeEntity::getPostInTime, requestVo.getIsWarehouseTime(), requestVo.getIsReturnCustomerTime());
            wrapperDay.between(SheinCodeEntity::getPostInTime, requestVo.getIsWarehouseTime(), requestVo.getIsReturnCustomerTime());
        } else {
            // 如果用户未输入时间值，则默认当天
            // 获取当前日期的 00:00:00 和 23:59:59
            LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
            LocalDateTime endOfDay = LocalDate.now().atTime(23, 59, 59); // 确保精确到秒级，不带纳秒
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String startTime = startOfDay.format(formatter); // 2025-03-03 00:00:00
            String endTime = endOfDay.format(formatter); // 2025-03-03 23:59:59

            requestVo.setIsWarehouseTime(startTime);
            requestVo.setIsReturnCustomerTime(endTime);
            wrapper.between(SheinCodeEntity::getPostInTime, startTime, endTime);
            wrapperDay.between(SheinCodeEntity::getPostInTime, startTime, endTime);
        }
        // 根据起始节点查询
        if (Objects.nonNull(requestVo.getStartNode()) && Objects.nonNull(requestVo.getEndNode())) {
            // wrapper.between(SheinCodeEntity::getStatus, requestVo.getStartNode(), requestVo.getEndNode());
            wrapper.eq(SheinCodeEntity::getStatus, requestVo.getEndNode());
            wrapperDay.eq(SheinCodeEntity::getStatus, requestVo.getEndNode());
        }
        // 根据合作商查询
        if (Objects.nonNull(requestVo.getAuthId())) {
            wrapper.eq(SheinCodeEntity::getAuthId, requestVo.getAuthId());
            wrapperDay.eq(SheinCodeEntity::getAuthId, requestVo.getAuthId());
        }
        // 根据仓库查询
        if (Objects.nonNull(requestVo.getWarehouseId())) {
            wrapper.eq(SheinCodeEntity::getWarehouseId, requestVo.getWarehouseId());
            wrapperDay.eq(SheinCodeEntity::getWarehouseId, requestVo.getWarehouseId());
        }
        // 根据司机id查询
//        if (Objects.nonNull(requestVo.getDriverId())) {
//            wrapper.eq(SheinCodeEntity::getDriverId, requestVo.getDriverId());
//            wrapperDay.eq(SheinCodeEntity::getDriverId, requestVo.getDriverId());
//        }
        // 根据驿站类型查询
//        if (Objects.nonNull(requestVo.getIsPudo())) {
//            wrapper.eq(PostEntity::getPudo, requestVo.getIsPudo());
//            wrapperDay.eq(PostEntity::getPudo, requestVo.getIsPudo());
//            wrapper.leftJoin(PostEntity.class, PostEntity::getPostId, SheinCodeEntity::getPostId);
//            wrapperDay.leftJoin(PostEntity.class, PostEntity::getPostId, SheinCodeEntity::getPostId);
//        }
        // 根据超时小时查询
        if (Objects.nonNull(requestVo.getTimeoutHour())) {
            // 计算超时的秒数
            long overTimeSeconds = requestVo.getTimeoutHour() * 3600L; // 小时转秒
            wrapper.apply("TIMESTAMPDIFF(SECOND, t.post_in_time, t.return_customer_time) > {0}", overTimeSeconds);
            wrapperDay.apply("TIMESTAMPDIFF(SECOND, t.post_in_time, t.return_customer_time) > {0}", overTimeSeconds);
        }

        // 获取总单数
        Long totalOrderCount = sheinCodeMapper.selectCount(wrapper);

        if (null == totalOrderCount || totalOrderCount == 0) {
            return R.ok();
        }

        // 记录单日签收量 (最后节点时间-开始节点时间)>7天
        wrapperDay.apply("TIMESTAMPDIFF(SECOND, post_in_time, return_customer_time) <= 7 * 86400");
        wrapperDay.eq(SheinCodeEntity::getStatus, 10);
        wrapperDay.groupBy("receive_day, t.auth_id, t.warehouse_id");  // 分组
        wrapperDay.orderByAsc("receive_day, t.auth_id, t.warehouse_id");    // 排序
        List<Map<String, Object>> dailyStats = sheinCodeMapper.selectMaps(wrapperDay);
        // List<Map<String, Object>> dailyStats = sheinCodeMapper.selectDayCount("2024-01-01", "2024-01-07");


        // 计算每日签收率
        List<Map<String, Object>> result = new ArrayList<>();
        for (Map<String, Object> dailyStat : dailyStats) {
            BigDecimal receiveDay = (BigDecimal) dailyStat.get("receive_day");
            Integer auId = (Integer) dailyStat.get("auth_id");
            Integer warehouseId = (Integer) dailyStat.get("warehouse_id");
            Long ordersDay = ((Long) dailyStat.get("orders_day"));
            BigDecimal ordersReceived = ((BigDecimal) dailyStat.get("orders_received"));

            // 计算每日签收率
            BigDecimal dailyReceiveRate = (ordersReceived.multiply(BigDecimal.valueOf(100.0))).divide(BigDecimal.valueOf(totalOrderCount), 2, RoundingMode.HALF_UP);

            // ** 新增百分比筛选**
            if (Objects.nonNull(requestVo.getPercentage()) &&
                    dailyReceiveRate.compareTo(BigDecimal.valueOf(requestVo.getPercentage())) < 0) {
                continue; // 如果低于阈值，跳过当前记录
            }

            // 将结果存入最终返回的数据
            Map<String, Object> resultMap = new HashMap<>();
            // 天数
            resultMap.put("daySpan", receiveDay);
            // 合作商ID
            resultMap.put("auId", auId);
            // 仓库ID
            resultMap.put("warehouseId", warehouseId);
            // 当日订单累加返仓数量
            resultMap.put("ordersReceived", ordersReceived.setScale(0, RoundingMode.HALF_UP));
            // 时间范围内总单数
            resultMap.put("totalOrderCount", totalOrderCount);
            // 当日签收率
            resultMap.put("dailyReceiveRate", dailyReceiveRate);
            // 当日订单返仓数量
            resultMap.put("ordersDay", ordersDay);
            result.add(resultMap);
        }
        return R.ok(result);
    }


    /**
     * 导出监控寄出-签收的百分比
     *
     * @param requestVo 参数对象
     * @param
     * @return
     */
    @Override
    public List<SheinPickSignExcelVo> exportPickSign(SheinPickSignBo requestVo) {
        // 查询总订单数量
        MPJLambdaWrapper<SheinCodeEntity> wrapper = new MPJLambdaWrapper<>();
        MPJLambdaWrapper<SheinCodeEntity> wrapperDay = new MPJLambdaWrapper<SheinCodeEntity>()
                .select("FLOOR(TIMESTAMPDIFF(SECOND, post_in_time, return_customer_time) / 86400) + 1 AS receive_day",  // 按天数分组
                        "auth_id",  // 合作商ID
                        "warehouse_id",  // 仓库ID
                        "COUNT(*) AS orders_day",   // 每日签收订单数
                        "SUM(count(*)) OVER (PARTITION BY auth_id, warehouse_id ORDER BY FLOOR(TIMESTAMPDIFF(SECOND, post_in_time, return_customer_time) / 86400) + 1) AS orders_received" // 累计每日签收订单数
                );  // 只查询已签收的订单

        // **追加查询条件**
        // 先根据时间区间查询
        if (null != requestVo.getIsWarehouseTime() && null != requestVo.getIsReturnCustomerTime()) {
            // 给这两个时间添加00:00:00 和 23:59:59
            requestVo.setIsWarehouseTime(requestVo.getIsWarehouseTime() + " 00:00:00");
            requestVo.setIsReturnCustomerTime(requestVo.getIsReturnCustomerTime() + " 23:59:59");
            wrapper.between(SheinCodeEntity::getPostInTime, requestVo.getIsWarehouseTime(), requestVo.getIsReturnCustomerTime());
            wrapperDay.between(SheinCodeEntity::getPostInTime, requestVo.getIsWarehouseTime(), requestVo.getIsReturnCustomerTime());
        } else {
            // 如果用户未输入时间值，则默认当天00:00:00 到23:59:59
            LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
            LocalDateTime endOfDay = LocalDate.now().atTime(23, 59, 59);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String startTime = startOfDay.format(formatter);
            String endTime = endOfDay.format(formatter);
            requestVo.setIsWarehouseTime(startTime);
            requestVo.setIsReturnCustomerTime(endTime);

            wrapper.between(SheinCodeEntity::getPostInTime, startTime, endTime);
            wrapperDay.between(SheinCodeEntity::getPostInTime, startTime, endTime);
        }
        // 根据起始节点查询
        if (Objects.nonNull(requestVo.getStartNode()) && Objects.nonNull(requestVo.getEndNode())) {
            // wrapper.between(SheinCodeEntity::getStatus, requestVo.getStartNode(), requestVo.getEndNode());
            wrapper.eq(SheinCodeEntity::getStatus, requestVo.getEndNode());
            wrapperDay.eq(SheinCodeEntity::getStatus, requestVo.getEndNode());
        }
        // 根据合作商查询
        if (Objects.nonNull(requestVo.getAuthId())) {
            wrapper.eq(SheinCodeEntity::getAuthId, requestVo.getAuthId());
            wrapperDay.eq(SheinCodeEntity::getAuthId, requestVo.getAuthId());
        }
        // 根据仓库查询
        if (Objects.nonNull(requestVo.getWarehouseId())) {
            wrapper.eq(SheinCodeEntity::getWarehouseId, requestVo.getWarehouseId());
            wrapperDay.eq(SheinCodeEntity::getWarehouseId, requestVo.getWarehouseId());
        }
        // 根据司机id查询
//        if (Objects.nonNull(requestVo.getDriverId())) {
//            wrapper.eq(SheinCodeEntity::getDriverId, requestVo.getDriverId());
//            wrapperDay.eq(SheinCodeEntity::getDriverId, requestVo.getDriverId());
//        }
        // 根据驿站类型查询
//        if (Objects.nonNull(requestVo.getIsPudo())) {
//            //wrapper.leftJoin(PostEntity.class, PostEntity::getPostId, SheinCodeEntity::getPostId);
//            //wrapper.eq(PostEntity::getPudo, requestVo.getIsPudo());
//            wrapperDay.eq(PostEntity::getPudo, requestVo.getIsPudo());
//            wrapperDay.leftJoin(PostEntity.class, PostEntity::getPostId, SheinCodeEntity::getPostId);
//        }
        // 根据超时小时查询
        if (Objects.nonNull(requestVo.getTimeoutHour())) {
            // 计算超时的秒数
            long overTimeSeconds = requestVo.getTimeoutHour() * 3600L; // 小时转秒
            wrapper.apply("TIMESTAMPDIFF(SECOND, t.post_in_time, t.return_customer_time) > {0}", overTimeSeconds);
            wrapperDay.apply("TIMESTAMPDIFF(SECOND, t.post_in_time, t.return_customer_time) > {0}", overTimeSeconds);
        }

        // **获取总订单数**
        Long totalOrderCount = null;

        // **查询每日签收数量** (最后节点时间-开始节点时间)>7天
        wrapperDay.apply("TIMESTAMPDIFF(SECOND, post_in_time, return_customer_time) <= 7 * 86400");
        wrapperDay.eq(SheinCodeEntity::getStatus, 10);
        wrapperDay.groupBy("receive_day, t.auth_id, t.warehouse_id");
        wrapperDay.orderByAsc("receive_day, t.auth_id, t.warehouse_id");
        // 记录每日签收率数据
        List<Map<String, Object>> dailyStats = null;

        // 查询所有合作商名称
        Map<Integer, String> authMap = apiAuthMapper.selectList(new QueryWrapper<ApiAuthEntity>())
                .stream().collect(Collectors.toMap(ApiAuthEntity::getId, ApiAuthEntity::getPartnerName));
        // 查询所有仓库名称
        Map<Integer, String> warehouseMap = warehouseMapper.selectList(new QueryWrapper<WarehouseEntity>())
                .stream().collect(Collectors.toMap(WarehouseEntity::getWarehouseId, WarehouseEntity::getWarehouseName));

        // **一次性查出所有符合条件的订单**
        MPJLambdaWrapper<SheinCodeEntity> allOrdersWrapper = new MPJLambdaWrapper<>();
        allOrdersWrapper.select(SheinCodeEntity::getSheinCode, SheinCodeEntity::getPostInTime,
                SheinCodeEntity::getReturnCustomerTime, SheinCodeEntity::getAuthId,
                SheinCodeEntity::getWarehouseId);
        allOrdersWrapper.between(SheinCodeEntity::getPostInTime, requestVo.getIsWarehouseTime(), requestVo.getIsReturnCustomerTime());
        // isData: 0 表示导出正常数据，1 表示导出异常数据
        if (requestVo.getIsData() == 0) {
            // 只要导出正常数据的情况下才需要查询每日签收率数据
            totalOrderCount = this.baseMapper.selectCount(wrapper);
            dailyStats = sheinCodeMapper.selectMaps(wrapperDay);
            allOrdersWrapper.eq(SheinCodeEntity::getStatus, 10);
        } else {
            // 查询“订单状态不等于10” 或者 “订单状态等于10 但 (签收时间 - 入站时间) > 7天”
            allOrdersWrapper.and(a ->
                    a.ne(SheinCodeEntity::getStatus, 10)
                            .or()
                            .apply("status = 10 and TIMESTAMPDIFF(SECOND, post_in_time, return_customer_time) > 7 * 86400"));
            // 如果是异常数据则直接返回，无需进行下一步操作
            //allOrdersWrapper.ne(SheinCodeEntity::getStatus, 10);
            List<SheinCodeEntity> sheinCodeEntityList = sheinCodeMapper.selectList(allOrdersWrapper);
            List<SheinPickSignExcelVo> exportList2 = new ArrayList<>();
            for (SheinCodeEntity sheinCodeEntity : sheinCodeEntityList) {
                SheinPickSignExcelVo sheinPickSignExcelVo = new SheinPickSignExcelVo();
                BeanUtil.copyProperties(sheinCodeEntity, sheinPickSignExcelVo);
                sheinPickSignExcelVo.setAuthNmae(authMap.get(sheinCodeEntity.getAuthId()));
                sheinPickSignExcelVo.setWarehouseName(warehouseMap.get(sheinCodeEntity.getWarehouseId()));
                exportList2.add(sheinPickSignExcelVo);
            }
            return exportList2;
        }

        // 执行查询订单信息
        List<SheinCodeEntity> allOrders = sheinCodeMapper.selectList(allOrdersWrapper);


        // **按 auth_id + warehouse_id 分组订单**
        Map<String, List<SheinCodeEntity>> ordersMap = allOrders.stream()
                .collect(Collectors.groupingBy(o -> o.getAuthId() + "_" + o.getWarehouseId()));

        List<SheinPickSignExcelVo> exportList = new ArrayList<>();

        // 记录已导出的订单编号，避免重复
        Set<String> exportedSheinCodes = new HashSet<>();

        for (Map<String, Object> dailyStat : dailyStats) {
            BigDecimal receiveDay = (BigDecimal) dailyStat.get("receive_day");
            Integer authId = (Integer) dailyStat.get("auth_id");
            Integer warehouseId = (Integer) dailyStat.get("warehouse_id");
            Long ordersDay = ((Long) dailyStat.get("orders_day"));
            BigDecimal ordersReceived = ((BigDecimal) dailyStat.get("orders_received"));

            // 计算每日签收率
            BigDecimal dailyReceiveRate = (ordersReceived.multiply(BigDecimal.valueOf(100.0)))
                    .divide(BigDecimal.valueOf(totalOrderCount), 2, RoundingMode.HALF_UP);

            // ** 新增百分比筛选**
            if (Objects.nonNull(requestVo.getPercentage()) &&
                    dailyReceiveRate.compareTo(BigDecimal.valueOf(requestVo.getPercentage())) < 0) {
                continue; // 如果低于阈值，跳过当前记录
            }

            // **直接从 map 里拿订单，避免循环查询数据库**
            List<SheinCodeEntity> orders = ordersMap.getOrDefault(authId + "_" + warehouseId, new ArrayList<>());

            // **筛选符合 receiveDay 条件的订单**
            List<SheinCodeEntity> filteredOrders = orders.stream()
                    .filter(order -> {
                        long diffSeconds = Duration.between(order.getPostInTime(), order.getReturnCustomerTime()).getSeconds();
                        return diffSeconds <= receiveDay.multiply(BigDecimal.valueOf(86400)).longValue();
                    })
                    .collect(Collectors.toList());

            for (SheinCodeEntity order : filteredOrders) {
                if (exportedSheinCodes.contains(order.getSheinCode())) {
                    continue;  // 订单已经导出，跳过
                }
                SheinPickSignExcelVo dto = new SheinPickSignExcelVo();
                dto.setDaySpan(receiveDay);
                dto.setAuthNmae(authMap.get(authId));
                dto.setWarehouseName(warehouseMap.get(warehouseId));
                dto.setSheinCode(order.getSheinCode());
                dto.setPostInTime(order.getPostInTime());
                dto.setReturnCustomerTime(order.getReturnCustomerTime());
                dto.setOrdersReceived(ordersReceived);
                dto.setTotalOrderCount(totalOrderCount);
                dto.setDailyReceiveRate(dailyReceiveRate);
                dto.setOrdersDay(ordersDay);
                exportList.add(dto);
                exportedSheinCodes.add(order.getSheinCode()); // 记录已导出的订单
            }
        }


        return exportList;
    }

    //推送订单到NB
    @Override
    @Transactional
    public R pushOrderToNb(ApiOrder order, String apiKey, String timestamp) {
        String URL = TkzjConstants.API_URL_ + "/create/label";
        String[] result = checkCreateOrderPara(order, apiKey, timestamp);
        if (result.length > 0) {
            String msg = Arrays.toString(result);
            recordOrderData(order.getReferenceNo()+":ERROR", msg);
            return R.failed(msg);
        }
        TmsCustomerEntity tmsCustomerByToken = remoteTmsService.getTmsCustomerByToken(apiKey);
        //直接下单到TMS
        if (Objects.nonNull(order.getShipMode()) && tmsCustomerByToken != null) {
            //记录请求报文
            recordOrderData(order.getReferenceNo(), JSONObject.toJSONString(order));

            R r = remoteTmsService.pushOrder(order);
            if (!r.isOk()) {
                recordOrderData(order.getReferenceNo()+":ERROR", r.getMsg());
                return R.failed(r.getMsg());
            }
            ZdjApiVo zdjApiVo = OrderTools.parseTmsResult(r);
            ArrayList<subOrderVo> subOrderVos = new ArrayList<>();
            HashMap<String, String> subOrderNos = zdjApiVo.getSubOrderNos();
            for (Map.Entry<String, String> entry : subOrderNos.entrySet()) {
                subOrderVo subOrderVo = new subOrderVo();
                subOrderVo.setBoxNum(entry.getKey());
                subOrderVo.setSubOrderNo(entry.getValue());
                subOrderVo.setUrl(URL + "?trackingNo=" + entry.getValue());
                subOrderVos.add(subOrderVo);
            }
            createResultVo createResultVo = new createResultVo(zdjApiVo.getCustomerOrderNumber(), zdjApiVo.getTrackingNo(),
                    URL + "?trackingNo=" + zdjApiVo.getTrackingNo(), subOrderVos);
            return r.isOk() ? R.ok(createResultVo, "success") : R.failed(r.getMsg());

        }
        //以下代码暂时不用
        SheinCodeEntity newOrder = new SheinCodeEntity();
        newOrder.setSheinCode(generalNewOrderNo(order.getChannelCode()));
        newOrder.setStatus(OrderConstants.STATUS_1_CREATE);
        newOrder.setAuthId(order.getAuthId());
        newOrder.setCustomNo(order.getReferenceNo());
        newOrder.setCountryId(order.getCountryId());
        newOrder.setProvinceId(order.getProvinceId());
        newOrder.setCityId(order.getCityId());
        newOrder.setConsignee(order.getConsigneeName());
        newOrder.setAddress(order.getConsigneeAddress());
        newOrder.setMobile(order.getConsigneePhone());
        newOrder.setZip(order.getConsigneePostcode());
        newOrder.setEmail(order.getConsigneeEmail());
        newOrder.setSenderCountryName(order.getShipperCountryCode());
        newOrder.setSenderProvinceName(order.getShipperProvince());
        newOrder.setSenderCityName(order.getShipperCity());
        newOrder.setSenderAddress(order.getShipperAddress());
        newOrder.setSenderName(order.getShipperName());
        newOrder.setSenderMobile(order.getShipperPhone());
        newOrder.setSenderPostalcode(order.getShipperPostcode());
        newOrder.setPrice(order.getPrice());
        newOrder.setNote(order.getMemo());
        newOrder.setCreateDate(LocalDateTime.now());
        //计算来源城市
        if (StringUtils.isNotBlank(order.getShipperPostcode())) {
            newOrder.setSourceCity(getSourceCity(order.getShipperPostcode()));
        }
        String baseOrder = newOrder.getSheinCode();
        //判断是否一票多件
        synchronized (Object.class) {
            List<SubOrderEntity> apiOrderVolumeList = order.getApiOrderVolumeList();
            if (apiOrderVolumeList.size() > 1) {
                newOrder.setMany(true);
                for (int i = 0; i < apiOrderVolumeList.size(); i++) {
                    SubOrderEntity volume = apiOrderVolumeList.get(i);
                    StringBuffer str = new StringBuffer();
                    str.append(baseOrder);
                    str.append(String.format("%03d", i + 1));
                    newOrder.setSheinCode(str.toString());
                    SubOrderEntity subOrderEntity = new SubOrderEntity();
                    subOrderEntity.setOrderNo(baseOrder);
                    subOrderEntity.setSubNo(str.toString());
                    subOrderEntity.setLength(volume.getLength());
                    subOrderEntity.setWidth(volume.getWidth());
                    subOrderEntity.setHeight(volume.getHeight());
                    subOrderEntity.setWeight(volume.getWeight());
                    subOrderEntity.setBagNum(volume.getBagNum());
                    subOrderEntity.setStatus(OrderConstants.STATUS_1_CREATE);
                    subOrderEntity.setCreateDate(LocalDateTime.now());
                    subOrderService.save(subOrderEntity);
                    if (newOrder.getId() != null) {
                        newOrder.setId(newOrder.getId() + 1);
                    }
                    this.save(newOrder);

                }
                newOrder.setId(newOrder.getId() + apiOrderVolumeList.size() + 1);
                newOrder.setSheinCode(baseOrder);
            }
            this.save(newOrder);
            //     return R.ok(new createResultVo(newOrder.getCustomNo(), newOrder.getSheinCode(), URL + "?trackingNo=" + newOrder.getSheinCode()), "success");
            return R.ok();
        }
    }

    //简单报文记录，仅供调试用
    private void recordOrderData(String code, String detail) {
        PushEntity pushEntity = new PushEntity();
        pushEntity.setCode((code + "TMS_time" + System.currentTimeMillis()));
        pushEntity.setDetail(detail);
        pushService.save(pushEntity);
    }


    private String generalNewOrderNo(String channelCode) {
        synchronized (Object.class) {
            ApiAuthEntity auth = apiAuthService.getAuthByCode(channelCode);
            Integer count = auth.getCount();
            String yyyy = OrderTools.getFormattedDateMinusDays("yyyy", 0);
            StringBuffer str = new StringBuffer();
            str.append("N");
            str.append(yyyy.substring(yyyy.length() - 2));
            str.append("0");
            str.append(auth.getId());
            str.append("R");
            str.append(String.format("%07d", count == 0 ? 1 : count + 1));
            //更新当前订单数量
            auth.setCount(count + 1);
            apiAuthService.updateById(auth);
            return str.toString();
        }

    }


    private String[] checkCreateOrderPara(ApiOrder order, String apiKey, String timestamp) {
        //校验订单信息
        String[] message1 = VerifyFieldUtils.verifyFieldData(order);
        String[] message2 = OrderValidator.validateOrderList(order.getApiOrderVolumeList(), order.getApiOrderItemList());
        //校验订单是否该推送到NB
        String[] message3 = checkOrder(order, apiKey, timestamp);
        String[] result = OrderTools.copyArrayToEnd(message1, message2);
        result = OrderTools.copyArrayToEnd(result, message3);
        return result;
    }

    //订单拦截
    @Override
    public R blockOrder(String logiNo, String apiKey) {
        //查询客户是否存在
        ArrayList<String> messageList = new ArrayList<>();
        ApiAuthEntity apiAuthEntity = apiAuthService.getAuthByToken(apiKey);
        if (apiAuthEntity == null | StringUtils.isBlank(apiKey)) {
            messageList.add("apiKey不存在！");
        }
        if (StringUtils.isNotBlank(logiNo) && logiNo.length() > 32) {
            messageList.add("跟踪单号长度不能超过32位！");
        }
        LambdaQueryWrapper<SheinCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SheinCodeEntity::getSheinCode, logiNo)
                .eq(SheinCodeEntity::getAuthId, apiAuthEntity == null ? null : apiAuthEntity.getId())
                .last("limit 1");
        SheinCodeEntity one = this.getOne(wrapper);
        if (one == null) {
            messageList.add("跟踪单号不存在！");
        }
        if (!messageList.isEmpty()) {
            return R.failed(messageList.toArray(new String[0]));
        }
        LambdaUpdateWrapper<SheinCodeEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(SheinCodeEntity::getBlockStatus, true)
                .set(SheinCodeEntity::getBlockTime, LocalDateTime.now())
                .eq(SheinCodeEntity::getSheinCode, logiNo)
                .eq(SheinCodeEntity::getAuthId, apiAuthEntity.getId());
        this.update(updateWrapper);
        //判断是否一票多件
        if (one.getMany() != null && one.getMany()) {
            ArrayList<SubOrderEntity> list = subOrderService.getSubOrderListByOrderNo(logiNo);
            for (SubOrderEntity subOrder : list) {
                LambdaUpdateWrapper<SheinCodeEntity> updateWrapper2 = new LambdaUpdateWrapper<>();
                updateWrapper2.set(SheinCodeEntity::getBlockStatus, true)
                        .set(SheinCodeEntity::getBlockTime, LocalDateTime.now())
                        .eq(SheinCodeEntity::getSheinCode, subOrder.getSubNo())
                        .eq(SheinCodeEntity::getAuthId, apiAuthEntity.getId());
                this.update(updateWrapper2);
            }
        }
        return R.ok(null, "success");
    }


    //删除订单
    @Override
    public R delOrder(String logiNo, String apiKey) {
        //查询客户是否存在
        ArrayList<String> messageList = new ArrayList<>();
        ApiAuthEntity apiAuthEntity = apiAuthService.getAuthByToken(apiKey);
        TmsCustomerEntity tmsCustomerByToken = remoteTmsService.getTmsCustomerByToken(apiKey);
        if (apiAuthEntity == null && tmsCustomerByToken == null) {
            messageList.add("apiKey不存在！");
        }
//        if (StringUtils.isNotBlank(logiNo) && logiNo.length() > 32) {
//            messageList.add("跟踪单号长度不能超过32位！");
//        }

        //转发到TMS
        if (tmsCustomerByToken != null) {
            if (messageList.size() > 1) {
                return R.failed(messageList.toString());
            }
            R r = remoteTmsService.deleteOrder(logiNo);
            if (!r.isOk()) {
                messageList.add(r.getMsg());
                return R.failed(messageList.toString());
            }
            tmsOrderLogService.saveLog(logiNo, OrderLogConstants.DELETE, "", apiKey);
            return R.ok();
        }

        // 使用 QueryWrapper 构建查询条件
        LambdaQueryWrapper<SheinCodeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SheinCodeEntity::getSheinCode, logiNo)
                .eq(SheinCodeEntity::getAuthId,
                        apiAuthEntity == null ? null : apiAuthEntity.getId()).last("limit 1");
        SheinCodeEntity sheinCodeEntity = sheinCodeMapper.selectOne(queryWrapper);
        if (sheinCodeEntity != null) {
            if (sheinCodeEntity.getStatus() > 1) {
                messageList.add("订单已存在路由信息，无法删除");
            }
            SheinCodeEntity updateEntity = new SheinCodeEntity();
            updateEntity.setId(sheinCodeEntity.getId());
            updateEntity.setSheinCode(sheinCodeEntity.getSheinCode() + "_del");
            updateEntity.setIsDelete(1);
            updateEntity.setDeleteTime(LocalDateTime.now());
            sheinCodeMapper.updateById(updateEntity);

            //判断是否一票多件
            if (sheinCodeEntity.getMany() != null && sheinCodeEntity.getMany()) {
                ArrayList<SubOrderEntity> list = subOrderService.getSubOrderListByOrderNo(logiNo);
                for (SubOrderEntity subOrder : list) {
                    LambdaUpdateWrapper<SheinCodeEntity> updateWrapper2 = new LambdaUpdateWrapper<>();
                    updateWrapper2
                            .set(SheinCodeEntity::getSheinCode, subOrder.getSubNo() + "_del")
                            .set(SheinCodeEntity::getIsDelete, 1)
                            .set(SheinCodeEntity::getDeleteTime, LocalDateTime.now())
                            .eq(SheinCodeEntity::getSheinCode, subOrder.getSubNo())
                            .eq(SheinCodeEntity::getAuthId, apiAuthEntity.getId());
                    this.update(updateWrapper2);
                }
            }
            return R.ok();
        }
        messageList.add("订单不存在");
        return R.failed(messageList.toArray(new String[0]));
    }

    //校验包裹状态
    @Override
    public R checkOrderStatus(String orderNo) {
        SheinCodeEntity order = this.getOrder(orderNo);
        if (order == null) {
            return LocalizedR.failed("tkzj.zt.shein.code.order.does.not.exist", Optional.ofNullable(null));
        } else if (order.getStatus() == OrderConstants.STATUS_2_POST) {
            return R.failed(Boolean.FALSE, LocalizedR.getMessage("tkzj.zt.shein.code.the.order.is.inbound", new Object[]{OrderTools.formatLocalDateTime(order.getPostInTime())}));
        } else if (order.getStatus() == OrderConstants.STATUS_3_DRIVER) {
            return R.failed(Boolean.FALSE, LocalizedR.getMessage("tkzj.zt.shein.code.the.order.is.driver", new Object[]{OrderTools.formatLocalDateTime(order.getDriverTime())}));
        } else if (order.getStatus() == OrderConstants.STATUS_4_STORAGE) {
            return R.failed(Boolean.FALSE, LocalizedR.getMessage("tkzj.zt.shein.code.the.order.is.storage", new Object[]{OrderTools.formatLocalDateTime(order.getWarehouseTime())}));
        } else if (order.getStatus() == OrderConstants.STATUS_10_RETURN_CUSTOMER) {
            return R.failed(Boolean.FALSE, LocalizedR.getMessage("tkzj.zt.shein.code.the.order.is.return", new Object[]{OrderTools.formatLocalDateTime(order.getReturnCustomerTime())}));
        }
        return R.ok();
    }


    //新客户打印面单
    @Override
    public R printLabel(String trackingNo, HttpServletResponse response) {
        //查询客户是否存在
        ArrayList<String> messageList = new ArrayList<>();
        if (StringUtils.isBlank(trackingNo)) {
            messageList.add("trackingNo不能为空！");
        }
        if (StringUtils.isNotBlank(trackingNo) && trackingNo.length() > 32) {
            messageList.add("trackingNo长度不能超过32位！");
        }
        SheinCodeEntity code = getOrderByOrderNo(trackingNo);
        TmsCustomerOrderEntity customerOrder = remoteTmsService.getCustomerOrder(trackingNo, false);
        if (code == null && customerOrder == null) {
            messageList.add("trackingNo不存在！");
        }
        if (!messageList.isEmpty()) {
            return R.failed(messageList.toArray(new String[0]));
        }

        //转发到TMS
//        if (customerOrder != null) {
//            return printKpLabel(trackingNo, response);
//        }
        System.out.println("参数校验完成");
        //获取省市区
        CityEntity province = cityService.findCacheById(code.getProvinceId());
        CityEntity city = cityService.findCacheById(code.getCityId());
        CityEntity country = cityService.findCacheById(code.getCountryId());
        //设置面单填充参数
        Map<String, String> params = Maps.newHashMap();
        ApiAuthEntity auth = apiAuthService.getById(code.getAuthId());
        params.put("to", auth.getPartnerName());
        params.put("toAddress", code.getAddress() + "," + city.getEnName() + "," + province.getEnName() + "," + country.getEnName() + " " + code.getZip());
        params.put("toMobile", code.getMobile());
        String yyz = "YYZ";
        if ("AB".equalsIgnoreCase(code.getProvinceName()) || "Alberta".equalsIgnoreCase(code.getProvinceName())) {
            yyz = "YVR";
        }
        if ("BC".equalsIgnoreCase(code.getProvinceName()) || "British Columbia".equalsIgnoreCase(code.getProvinceName())) {
            yyz = "YVR";
        }
        if ("Toronto".equals(code.getSourceCity())) {
            yyz = "YYZ ";
        } else if ("Vancouver".equals(code.getSourceCity())) {
            yyz = "YVR";
        }
        params.put("from", code.getSenderName());
        params.put("fromAddress", code.getSenderAddress() + (StringUtils.isBlank(code.getSenderAddress2()) ? "" : (" " + code.getSenderAddress2()))
                + (StringUtils.isBlank(code.getSenderAddress3()) ? ""
                : (" " + code.getSenderAddress3())) + code.getSenderCityName() + ","
                + code.getSenderProvinceName() + "," + code.getSenderCountryName() + " " + code.getSenderPostalcode() + " "
                + code.getSenderMobile());
        params.put("fromMobile", code.getSenderMobile());

        params.put("logiNo", code.getSheinCode());
        params.put("createDate", DateFormatUtils.format(Date.from(code.getCreateDate().atZone(ZoneId.systemDefault()).toInstant()), "yyyy-MM-dd HH:mm"));
        params.put("YYZ", yyz);
        params.put("customerNo", code.getCustomNo());
        response.setContentType("application/pdf");
        //设置PDF参数
        BarCodeDto dto1 = new BarCodeDto(code.getSheinCode(), false, 1.25f, 55, 35, 165);
        dto1.setFontSize(15f);
        dto1.setBaseLine(25f);
        BarCodeDto dto2 = new BarCodeDto(code.getSheinCode(), false, 0.66f, 35, 140, 30);
        dto2.setFontSize(10f);
        dto2.setBaseLine(10f);
        List<BarCodeDto> barCodes = Lists.newArrayList(dto1, dto2);
        //生成面单
//        String projectRoot = System.getProperty("user.dir");
//        path = projectRoot + File.separator + "jynx-basic" + File.separator + "jynx-basic-biz" + File.separator + "tpl" + File.separator + "label_sf_20220316112233.pdf";
        //生成面单
        String URL = "https://nbexpress.oss-us-east-1.aliyuncs.com/tmsFile/label_sf_20220316112233.pdf";
        //判断是否一票多件

        if (code.getMany() != null && code.getMany()) {
            if (code.getSheinCode().length() > 13) {
                PdfUtils.toResponseStream(URL, params, barCodes, response);
                return R.ok();
            }
            ArrayList<SubOrderEntity> subOrderList = subOrderService.getSubOrderListByOrderNo(code.getSheinCode());
            if (subOrderList.isEmpty()) {
                messageList.add("trackingNo不存在！");
            }
            ArrayList<String> templates = new ArrayList<>();
            List<Map<String, String>> paramsList = new ArrayList<>();
            List<List<BarCodeDto>> barCodesList = new ArrayList<>();
            for (SubOrderEntity subOrderEntity : subOrderList) {
                templates.add(URL);
                // 复制原有的 params
                Map<String, String> newParams = new HashMap<>(params);
                newParams.put("logiNo", subOrderEntity.getSubNo());
                paramsList.add(newParams);
                BarCodeDto barPara1 = new BarCodeDto(subOrderEntity.getSubNo(), false, 1.3f, 55, 35, 165);
                barPara1.setFontSize(15f);
                barPara1.setBaseLine(25f);
                BarCodeDto barPara2 = new BarCodeDto(subOrderEntity.getSubNo(), false, 0.75f, 35, 140, 30);
                barPara2.setFontSize(10f);
                barPara2.setBaseLine(10f);
                List<BarCodeDto> barParaList = Lists.newArrayList(barPara1, barPara2);
                barCodesList.add(barParaList);
            }
//            ArrayList<String> templates = new ArrayList<>();
//            templates.add(URL);
//            List<Map<String, String>> paramsList= new ArrayList<>();
//            paramsList.add(params);
//            List<List<BarCodeDto>> barCodesList = new ArrayList<>();
//            barCodesList.add(barCodes);
            System.out.println("开始生成面单mergeAndOutputPdf");
            PdfUtilsToMany.mergeAndOutputPdf(templates, paramsList, barCodesList, null, null, response);
        } else {
            System.out.println("开始生成面单toResponseStream");
            PdfUtils.toResponseStream(URL, params, barCodes, response);
        }
        return R.ok();

    }


    //查询轨迹接口（新）
    @Override
    public R trackOrder(String trackingNo, String apiKey) {
        //查询客户是否存在
        ArrayList<String> messageList = new ArrayList<>();
        ApiAuthEntity apiAuthEntity = apiAuthService.getAuthByToken(apiKey);
        TmsCustomerEntity tmsCustomerByToken = remoteTmsService.getTmsCustomerByToken(apiKey);
        if (apiAuthEntity == null && tmsCustomerByToken == null) {
            messageList.add("apiKey不存在！");
        }
        if (StringUtils.isNotBlank(trackingNo) && trackingNo.length() > 32) {
            messageList.add("跟踪单号长度不能超过32位！");
        }
        SheinCodeEntity sheinCode = getOrderByOrderNo(trackingNo);
        TmsCustomerOrderEntity customerOrder = remoteTmsService.getCustomerOrder(trackingNo, false);
        if (sheinCode == null && customerOrder == null) {
            messageList.add("跟踪单号不存在！");
        }
        if (!messageList.isEmpty()) {
            return R.failed(messageList.toArray(new String[0]));
        }

        //转发到TMS
        if (tmsCustomerByToken != null) {
            return remoteTmsService.track(trackingNo);
        }
        if (sheinCode.getMany() != null && sheinCode.getMany()) {
            ArrayList<SubOrderEntity> list = subOrderService.getSubOrderListByOrderNo(sheinCode.getSheinCode());
            ArrayList<Object> routeList = new ArrayList<>();
            for (SubOrderEntity subOrder : list) {
                R routeInfo = getRouteInfo(getOrderByOrderNo(subOrder.getSubNo()), true);
                routeList.add(routeInfo.getData());
            }
            return R.ok(routeList);
        } else {
            return getRouteInfo(sheinCode, false);
        }
    }

    private R getRouteInfo(SheinCodeEntity sheinCode, Boolean many) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        JSONArray ja = new JSONArray();
        if (many) {
            JSONObject trackNoJo = new JSONObject();
            trackNoJo.put("trackingNo", sheinCode.getSheinCode());
            ja.add(trackNoJo);
        }

        int status = sheinCode.getStatus();
        if (sheinCode.getBlockStatus() != null && sheinCode.getBlockStatus()) {
            JSONObject jo = new JSONObject();
            jo.put("status", OrderConstants.STATUS_0_BLOCK_ORDER);
            jo.put("time", sheinCode.getBlockTime().format(formatter));
            jo.put("statusText", TrackConstants.STATUS_0_RETURN_BLOCK);
            ja.add(jo);
        }

        if (status >= OrderConstants.STATUS_1_CREATE) {
            JSONObject jo = new JSONObject();
            jo.put("status", OrderConstants.STATUS_1_CREATE);
            jo.put("time", sheinCode.getCreateDate().format(formatter));
            jo.put("statusText", TrackConstants.STATUS_1_CREATE);
            ja.add(jo);

        }
        //优先处理PUDO
        if (status >= 4 & sheinCode.getPostInTime() == null) {
            JSONObject jo = new JSONObject();
            jo.put("status", OrderConstants.STATUS_4_STORAGE);
            jo.put("time", sheinCode.getWarehouseTime().format(formatter));
            jo.put("statusText", TrackConstants.STATUS_4_STORAGE);
            ja.add(jo);
            if (status < OrderConstants.STATUS_10_RETURN_CUSTOMER) {
                return R.ok(ja);

            }
        }
        if (status >= OrderConstants.STATUS_10_RETURN_CUSTOMER & sheinCode.getPostInTime() == null) {
            JSONObject jo = new JSONObject();
            jo.put("status", OrderConstants.STATUS_10_RETURN_CUSTOMER);
            jo.put("time", sheinCode.getReturnCustomerTime().format(formatter));
            jo.put("statusText", TrackConstants.STATUS_10_RETURN_CUSTOMER);
            ja.add(jo);
            return R.ok(ja);

        }
        if (status >= OrderConstants.STATUS_2_POST & sheinCode.getPostInTime() != null) {
            JSONObject jo = new JSONObject();
            jo.put("status", 2);
            jo.put("time", sheinCode.getPostInTime().format(formatter));
            jo.put("statusText", TrackConstants.STATUS_2_POST);
            ja.add(jo);
        }

        //优先处理PUDO
        if (status >= OrderConstants.STATUS_4_STORAGE & sheinCode.getDriverTime() == null) {
            JSONObject jo = new JSONObject();
            jo.put("status", OrderConstants.STATUS_4_STORAGE);
            jo.put("time", sheinCode.getWarehouseTime().format(formatter));
            jo.put("statusText", TrackConstants.STATUS_4_STORAGE);
            ja.add(jo);
            if (status < OrderConstants.STATUS_10_RETURN_CUSTOMER) {
                return R.ok(ja);
            }
        }

        if (status >= OrderConstants.STATUS_10_RETURN_CUSTOMER & sheinCode.getDriverTime() == null) {
            JSONObject jo = new JSONObject();
            jo.put("status", OrderConstants.STATUS_10_RETURN_CUSTOMER);
            jo.put("time", sheinCode.getReturnCustomerTime().format(formatter));
            jo.put("statusText", TrackConstants.STATUS_10_RETURN_CUSTOMER);
            ja.add(jo);
            return R.ok(ja);
        }
        if (status >= OrderConstants.STATUS_3_DRIVER & sheinCode.getPostInTime() != null) {
            JSONObject jo = new JSONObject();
            jo.put("status", 3);
            jo.put("time", sheinCode.getDriverTime().format(formatter));
            jo.put("statusText", "In Transit");
            ja.add(jo);
        }
        if (status >= OrderConstants.STATUS_4_STORAGE & sheinCode.getPostInTime() != null) {
            JSONObject jo = new JSONObject();
            jo.put("status", OrderConstants.STATUS_4_STORAGE);
            jo.put("time", sheinCode.getWarehouseTime().format(formatter));
            jo.put("statusText", TrackConstants.STATUS_4_STORAGE);
            ja.add(jo);
        }

        if (status >= OrderConstants.STATUS_10_RETURN_CUSTOMER & sheinCode.getPostInTime() != null) {
            JSONObject jo = new JSONObject();
            jo.put("status", OrderConstants.STATUS_10_RETURN_CUSTOMER);
            jo.put("time", sheinCode.getReturnCustomerTime().format(formatter));
            jo.put("statusText", TrackConstants.STATUS_10_RETURN_CUSTOMER);
            ja.add(jo);
        }
        return R.ok(ja);
    }


    //新订单URL转PNG
    @Override
    public void orderToPng() {
        LambdaQueryWrapper<SheinCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.gt(SheinCodeEntity::getCreateDate, LocalDateTime.now().minusMinutes(13))
                .isNull(SheinCodeEntity::getLabelPng)
                .isNotNull(SheinCodeEntity::getLabelPath);
        List<SheinCodeEntity> list = sheinCodeMapper.selectList(wrapper);
        for (SheinCodeEntity sheinCodeEntity : list) {
            try {
                String pngUrl = HighResolutionPDFToPNG.convertPdfToPng(sheinCodeEntity.getLabelPath());
                LambdaUpdateWrapper<SheinCodeEntity> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.
                        set(SheinCodeEntity::getLabelPng, pngUrl).
                        eq(SheinCodeEntity::getId, sheinCodeEntity.getId());
                sheinCodeMapper.update(null, updateWrapper);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    //获取打印历史
    @Override
    public R getPrintHistory(Page page, String postId) {
        MPJLambdaWrapper<SheinCodeEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.select(SheinCodeEntity::getLabelPath, SheinCodeEntity::getPrintTime)
                .selectAs(PostEntity::getPostName, PrintHistoryVo::getPostName)
                .eq(SheinCodeEntity::getPrintPost, postId)
                .leftJoin(PostEntity.class, PostEntity::getPostId, SheinCodeEntity::getPostId)
                .orderByDesc(SheinCodeEntity::getPrintTime);
        return R.ok(sheinCodeMapper.selectJoinPage(page, PrintHistoryVo.class, wrapper));

    }


    private String[] checkOrder(ApiOrder order, String apiKey, String timestamp) {

        ArrayList<String> messageList = new ArrayList<>();
        //查询客户是否存在
        ApiAuthEntity apiAuthEntity = apiAuthService.getAuthByToken(apiKey);
        TmsCustomerEntity tmsCustomerByToken = remoteTmsService.getTmsCustomerByToken(apiKey);
        if (apiAuthEntity == null && tmsCustomerByToken == null) {
            messageList.add("apiKey不正确");

        }
        //查询渠道编码是否存在
        ApiAuthEntity apiChannelEntity = apiAuthService.getAuthByCode(order.getChannelCode());
        TmsCustomerEntity customerByCode = remoteTmsService.getTmsCustomerByCode(order.getChannelCode());
        if (customerByCode == null && apiChannelEntity == null) {
            messageList.add("渠道编码不正确！");
        }
        //查询是否重复推送
        SheinCodeEntity orderEntity = getOrderByCustomerNo(order.getReferenceNo());
        TmsCustomerOrderEntity customerOrder = remoteTmsService.getCustomerOrder(order.getReferenceNo(), true);
        if (orderEntity != null || customerOrder.getId() != null) {
            messageList.add(customerOrder.getCustomerOrderNumber() + "客户参考号已存在，请勿重复推送!");
        }

        //校验是否在服务范围
        CityEntity ConsigneeCountry = cityService.selectCityEntity(AreaGradeConstants.country, order.getConsigneeCountryCode());
        if (ConsigneeCountry == null) {
             messageList.add("consigneeCountryCode无效");
        }

        CityEntity consigneeProvince = cityService.selectScopeByCity(AreaGradeConstants.province, order.getConsigneeProvince());
        if (consigneeProvince == null) {
             messageList.add("consigneeProvince无效");
        }

        CityEntity consigneeCity = cityService.selectCityEntity(AreaGradeConstants.city, order.getConsigneeCity());
        if (consigneeCity == null) {
             messageList.add("consigneeCity无效");
        }
//        CityEntity consigneeCityIsExist = cityService.selectCityEntity(AreaGradeConstants.city, order.getConsigneeCity(), consigneeProvince==null?-1:consigneeProvince.getCityId());
//        if (consigneeCityIsExist == null) {
//            messageList.add("consigneeCity不在consigneeProvince管辖范围!");
//        }


        CityEntity shipperCountry = cityService.selectCityEntity(AreaGradeConstants.country, order.getShipperCountryCode());
        if (shipperCountry == null) {
             messageList.add("shipperCountryCode无效");
        }

        CityEntity shipperProvince = cityService.selectScopeByCity(AreaGradeConstants.province, order.getShipperProvince());
        if (shipperProvince == null) {
            messageList.add("shipperProvince无效");
        }

        CityEntity shipperCity = cityService.selectCityEntity(AreaGradeConstants.city, order.getShipperCity());
        if (shipperCity == null) {
            messageList.add("shipperCity无效");
        }

//        CityEntity shipperCityIsExist = cityService.selectCityEntity(AreaGradeConstants.city, order.getConsigneeCity(), shipperProvince==null?-1:shipperProvince.getCityId());
//        if (shipperCityIsExist == null) {
//            messageList.add("shipperCity不在shipperProvince管辖范围!");
//        }


        //判断箱号是否重复
        if (OrderTools.hasDuplicateBagNum(order.getApiOrderVolumeList())) {
            messageList.add("箱号不能重复!");
        }


        LocalDateTime date = null;
        try {
            // 自定义格式化器
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            // 将字符串转换为 LocalDateTime
            date = LocalDateTime.parse(timestamp, formatter);
//            LocalDateTime.parse(order.getEstimatedShippingTimeStart().toString(),formatter);
//            LocalDateTime.parse(order.getEstimatedShippingTimeEnd().toString(), formatter);
//            LocalDateTime.parse(order.getEstimatedArrivalTimeStart().toString(), formatter);
//            LocalDateTime.parse(order.getEstimatedArrivalTimeEnd().toString(), formatter);
            String estimatedShippingTimeStartStr = Optional.ofNullable(order.getEstimatedShippingTimeStart())
                    .map(t -> t.format(formatter))
                    .orElse(null);
            String estimatedShippingTimeEndStr = Optional.ofNullable(order.getEstimatedShippingTimeEnd())
                    .map(t -> t.format(formatter))
                    .orElse(null);
            String estimatedArrivalTimeStartStr = Optional.ofNullable(order.getEstimatedArrivalTimeStart())
                    .map(t -> t.format(formatter))
                    .orElse(null);
            String estimatedArrivalTimeEndStr = Optional.ofNullable(order.getEstimatedArrivalTimeEnd())
                    .map(t -> t.format(formatter))
                    .orElse(null);
        } catch (Exception e) {
            messageList.add("时间格式必须为yyyy-MM-dd HH:mm:ss");
        }
        if (OrderTools.isWithinCATime(timestamp)) {
            messageList.add("timestamp时间戳无效！");
        }
        order.setOrderTime(date);
        order.setAuthId(apiChannelEntity == null ? null : apiChannelEntity.getId());
        return messageList.toArray(new String[0]);
    }

    //通过客户单号查询订单
    private SheinCodeEntity getOrderByCustomerNo(String customerNo) {
        if (StringUtils.isBlank(customerNo)) {
            return null;
        }
        LambdaQueryWrapper<SheinCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SheinCodeEntity::getCustomNo, customerNo)
                .select(SheinCodeEntity::getCustomNo)
                .last("limit 1");
        return sheinCodeMapper.selectOne(wrapper);
    }


}