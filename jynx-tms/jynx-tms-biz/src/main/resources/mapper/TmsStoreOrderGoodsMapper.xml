<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsStoreOrderGoodsMapper">

    <resultMap id="tmsStoreOrderGoodsMap" type="com.jygjexp.jynx.tms.entity.TmsStoreOrderGoodsEntity">
        <id property="id" column="id"/>
        <result property="mainEntrustedOrder" column="main_entrusted_order"/>
        <result property="subEntrustedOrder" column="sub_entrusted_order"/>
        <result property="unitType" column="unit_type"/>
        <result property="length" column="length"/>
        <result property="width" column="width"/>
        <result property="height" column="height"/>
        <result property="weight" column="weight"/>
        <result property="goodValue" column="good_value"/>
        <result property="goodInfo" column="good_info"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <select id="getStoreOrderGoodsSummary" resultType="com.jygjexp.jynx.tms.vo.StoreOrderGoodsSummary">
        SELECT
        main_entrusted_order,
        unit_type,
        COUNT(1) total_counts,
        SUM(weight) AS total_weight,
        SUM(length * width * height) AS total_volume
        FROM tms_store_order_goods
        WHERE main_entrusted_order IN
        <foreach collection="mainEntrustedOrders" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        group by main_entrusted_order,unit_type
    </select>
</mapper>
