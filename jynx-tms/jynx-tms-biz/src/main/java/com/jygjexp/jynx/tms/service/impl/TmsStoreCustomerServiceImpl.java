package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.github.houbb.heaven.util.util.RandomUtil;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.admin.api.dto.*;
import com.jygjexp.jynx.admin.api.entity.SysDept;
import com.jygjexp.jynx.admin.api.entity.SysPost;
import com.jygjexp.jynx.admin.api.entity.SysRole;
import com.jygjexp.jynx.admin.api.entity.SysUser;
import com.jygjexp.jynx.admin.api.feign.RemoteDeptService;
import com.jygjexp.jynx.admin.api.feign.RemotePostService;
import com.jygjexp.jynx.admin.api.feign.RemoteRoleService;
import com.jygjexp.jynx.admin.api.feign.RemoteUserService;
import com.jygjexp.jynx.common.core.constant.CommonConstants;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.service.JynxUser;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.constants.StoreConstants;
import com.jygjexp.jynx.tms.dto.StoreCustomerExtendDTO;
import com.jygjexp.jynx.tms.dto.StoreMessageTraceDTO;
import com.jygjexp.jynx.tms.dto.StorePwdChangeDTO;
import com.jygjexp.jynx.tms.dto.TmsStoreCustomerDTO;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreCustomerEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreUserEntity;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.feign.RemoteTmsAppMobileService;
import com.jygjexp.jynx.tms.feign.RemoteTmsUpmsService;
import com.jygjexp.jynx.tms.mapper.TmsStoreCustomerMapper;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsStoreCustomerService;
import com.jygjexp.jynx.tms.service.TmsStoreUserService;
import com.jygjexp.jynx.tms.utils.AESUtil;
import com.jygjexp.jynx.tms.utils.MailSenderUtil;
import com.jygjexp.jynx.tms.utils.PhoneValidator;
import com.jygjexp.jynx.tms.utils.SnowflakeIdGenerator;
import com.jygjexp.jynx.tms.vo.StoreCustomerExtendVO;
import com.jygjexp.jynx.tms.vo.TmsStoreCustomerAdminVo;
import com.jygjexp.jynx.tms.vo.TmsStoreCustomerExcelVo;
import com.jygjexp.jynx.tms.vo.TmsStoreCustomerPageVo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 门店客户表
 *
 * <AUTHOR>
 * @date 2025-07-14 17:45:27
 */
@Service
@RequiredArgsConstructor
public class TmsStoreCustomerServiceImpl extends ServiceImpl<TmsStoreCustomerMapper, TmsStoreCustomerEntity> implements TmsStoreCustomerService {
    private static final PasswordEncoder ENCODER = new BCryptPasswordEncoder();
    private final RemoteTmsUpmsService remoteTmsUpmsService;
    private final RemoteDeptService remoteDeptService;
    private final RemoteRoleService remoteRoleService;
    private final RemotePostService remotePostService;
    private final RemoteUserService remoteUserService;
    private final StringRedisTemplate redisTemplate;
    private final RemoteTmsAppMobileService remoteTmsAppMobileService;
    private final TmsStoreUserService tmsStoreUserService;
    private final SnowflakeIdGenerator snowflakeIdGenerator;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveStoreCustomer(TmsStoreCustomerDTO tmsStoreCustomer) {
        // 判断用户名或手机号是否已存在
        if (remoteTmsUpmsService.getCustomerUserByPhoneOrUsername(tmsStoreCustomer.getPhone(), tmsStoreCustomer.getName()).getData()) {
            return LocalizedR.failed("tms.store.name.phone.already.exists", "");
        }
        // 判断客户代码是否存在
        if (this.count(new LambdaQueryWrapper<TmsStoreCustomerEntity>()
                .eq(TmsStoreCustomerEntity::getCode, tmsStoreCustomer.getCode())) > 0) {
            return LocalizedR.failed("tms.store.customer.code.already.exists", tmsStoreCustomer.getCode());
        }
        // 保存用户信息
        if (StrUtil.isBlank(tmsStoreCustomer.getPassword())){
            tmsStoreCustomer.setPassword("123456");
        }
        SysUser sysUser = saveUserInfo(tmsStoreCustomer);
        if (sysUser == null) {
            return LocalizedR.failed("tms.store.name.phone.already.exists", "");
        }
        try {
            // 保存门店端客户数据
            TmsStoreCustomerEntity tmsStoreCustomerEntity = new TmsStoreCustomerEntity();
            BeanUtils.copyProperties(tmsStoreCustomer, tmsStoreCustomerEntity);
            tmsStoreCustomerEntity.setUserId(sysUser.getUserId());
            this.save(tmsStoreCustomerEntity);

            //初始化客户余额
            TmsStoreBalanceEntity balance = new TmsStoreBalanceEntity();
            BigDecimal zero = BigDecimal.ZERO;
            String amountHash = DigestUtil.sha256Hex(zero.toString());
            balance.setStoreCustomerId(tmsStoreCustomerEntity.getId());
            balance.setAmount(zero);
            balance.setAmountHash(amountHash);
            Db.save(balance);

            return R.ok();
        } catch (Exception e) {
            remoteTmsUpmsService.userDelNoToken(new Long[]{sysUser.getUserId()});
            return R.failed();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateStoreCustomer(TmsStoreCustomerDTO tmsStoreCustomer) {
        // 修改客户表信息
        TmsStoreCustomerEntity tmsStoreCustomerEntity = new TmsStoreCustomerEntity();
        BeanUtils.copyProperties(tmsStoreCustomer, tmsStoreCustomerEntity);
        this.updateById(tmsStoreCustomerEntity);

        // 修改用户信息
        UserDTO userDTO = new UserDTO();
        userDTO.setUserId(tmsStoreCustomer.getUserId());
        userDTO.setUsername(tmsStoreCustomer.getName());
        userDTO.setName(tmsStoreCustomer.getName());
        userDTO.setEmail(tmsStoreCustomer.getEmail());
        remoteTmsUpmsService.updateUser(userDTO);

        return R.ok();
    }

    @Override
    public R updateStatus(TmsStoreCustomerEntity tmsStoreCustomer) {
        // 更新客户状态
        boolean updated = this.updateById(tmsStoreCustomer);
        if (updated) {
            // 通过客户ID获取对应的用户ID
            TmsStoreCustomerEntity employeeEntity = this.getOne(Wrappers.<TmsStoreCustomerEntity>lambdaQuery().eq(TmsStoreCustomerEntity::getId, tmsStoreCustomer.getId()));
            Long userId = employeeEntity.getUserId();
            if (userId != null) {
                // 更新用户状态
                if (tmsStoreCustomer.getStatus().equals(1)) {
                    // 启用用户状态
                    remoteTmsUpmsService.userEnables(Arrays.asList(userId));
                } else if (tmsStoreCustomer.getStatus().equals(0)) {
                    // 禁用用户
                    remoteTmsUpmsService.userLocks(Arrays.asList(userId));
                }
            }
            return R.ok();
        }
        return R.failed();
    }

    /**
     * 保存用户信息
     *
     * @param tmsStoreCustomer
     * @return
     */
    private SysUser saveUserInfo(TmsStoreCustomerDTO tmsStoreCustomer) {
        UserDTO sysUser = new UserDTO();
        // 设置部门信息
        SysDept dept = remoteDeptService.getDeptIdByName(CommonConstants.STORE_MANAGER_DEPT_NAME).getData();
        if (dept != null) {
            sysUser.setDeptId(dept.getDeptId());
        }
        // 获取角色信息
        List<SysRole> sysRoles = remoteRoleService.getAllRole().getData();
        SysRole sysRole = Optional.ofNullable(sysRoles)
                .orElse(Collections.emptyList())
                .stream()
                .filter(item -> CommonConstants.STORE_CUSTOMER_ROLE_CODE.equals(item.getRoleCode()))
                .findFirst()
                .orElse(null);
        if (sysRole != null) {
            sysUser.setRole(Arrays.asList(sysRole.getRoleId()));
        }
        // 获取岗位信息
        List<SysPost> sysPosts = remotePostService.getAllPost().getData();
        SysPost sysPost = Optional.ofNullable(sysPosts)
                .orElse(Collections.emptyList())
                .stream()
                .filter(item -> CommonConstants.STORE_MANAGER_POST_CODE.equals(item.getPostCode()))
                .findFirst()
                .orElse(null);
        if (sysPost != null) {
            sysUser.setPost(Arrays.asList(sysPost.getPostId()));
        }
        // 设置用户基本信息
        sysUser.setUsername(tmsStoreCustomer.getName());
        sysUser.setName(tmsStoreCustomer.getName());
        sysUser.setPhone(tmsStoreCustomer.getPhone());
        sysUser.setEmail(tmsStoreCustomer.getEmail());
        sysUser.setPassword(tmsStoreCustomer.getPassword() != null ? tmsStoreCustomer.getPassword() : "123456");
        sysUser.setLockFlag("0");
        remoteTmsUpmsService.addUserNoToken(sysUser);

        // 获取新增加的用户
        return remoteTmsUpmsService.getCustomerUserByPhone(tmsStoreCustomer.getPhone()).getData();
    }

    @Override
    public StoreCustomerExtendVO getMessageTraceConfig() {
        Long userId = SecurityUtils.getUser().getId();
        TmsStoreCustomerEntity tmsStoreCustomerEntity = baseMapper.getStoreCustomerByUserId(userId);
        if (null == tmsStoreCustomerEntity) {
            return null;
        }
        StoreCustomerExtendVO storeCustomerExtendVO = new StoreCustomerExtendVO();
        storeCustomerExtendVO.setId(tmsStoreCustomerEntity.getId());
        String extend = tmsStoreCustomerEntity.getExtend();
        if (StrUtil.isBlank(extend)) {
            return storeCustomerExtendVO;
        }
        StoreCustomerExtendVO notifyConfigDTO = JSONObject.parseObject(extend, StoreCustomerExtendVO.class);
        notifyConfigDTO.setId(tmsStoreCustomerEntity.getId());
        return notifyConfigDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMessageTraceConfig(StoreCustomerExtendVO customerExtendVO) {
        TmsStoreCustomerEntity tmsStoreCustomerEntity = baseMapper.selectById(customerExtendVO.getId());
        if (null == tmsStoreCustomerEntity) {
            return false;
        }
        String extend = tmsStoreCustomerEntity.getExtend();
        StoreCustomerExtendDTO extendDTO = null;
        if (StrUtil.isNotBlank(extend)) {
            extendDTO = JSONObject.parseObject(extend, StoreCustomerExtendDTO.class);
        } else {
            extendDTO = new StoreCustomerExtendDTO();
        }
        StoreMessageTraceDTO notifyConfig = customerExtendVO.getNotifyConfig();
        extendDTO.setNotifyConfig(notifyConfig);

        TmsStoreCustomerEntity updateEntity = new TmsStoreCustomerEntity();
        updateEntity.setId(tmsStoreCustomerEntity.getId());
        updateEntity.setExtend(JSON.toJSONString(extendDTO));
        return baseMapper.updateById(updateEntity) > 0;
    }

    @Override
    public boolean getEmailCode(String email) {
        if (!Validator.isEmail(email)) {
            // TODO @国际化处理@
            throw new CustomBusinessException("邮箱不合法");
        }
        Long userId = SecurityUtils.getUser().getId();
        TmsStoreCustomerEntity tmsStoreCustomerEntity = baseMapper.getStoreCustomerByUserId(userId);
        if (null == tmsStoreCustomerEntity) {
            // TODO @国际化处理@
            throw new CustomBusinessException("门店客户不存在，不支持操作");
        }
        if (!email.equals(tmsStoreCustomerEntity.getEmail())) {
            // TODO @国际化处理@
            throw new CustomBusinessException("邮箱不匹配");
        }
        String code = RandomUtil.randomNumber(6);
        redisTemplate.opsForValue().set(StoreConstants.RedisConstants.getStoreEmailUpdateKey(userId),
                code, 5, TimeUnit.MINUTES);
        return MailSenderUtil.sendMail(email, code);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateEmail(String email, String code) {
        if (StrUtil.isBlank(email) || StrUtil.isBlank(code)) {
            return false;
        }
        if (!Validator.isEmail(email)) {
            // TODO @国际化处理@
            throw new CustomBusinessException("邮箱不合法");
        }
        Long userId = SecurityUtils.getUser().getId();
        TmsStoreCustomerEntity tmsStoreCustomerEntity = baseMapper.getStoreCustomerByUserId(userId);
        if (null == tmsStoreCustomerEntity) {
            // TODO @国际化处理@
            throw new CustomBusinessException("门店客户不存在，不支持操作");
        }
        String emailDB = tmsStoreCustomerEntity.getEmail();
        if (emailDB.equals(email)) {
            return true;
        }
        TmsStoreCustomerEntity storeCustomerDB = baseMapper.getStoreCustomerByEmail(email);
        if (null != storeCustomerDB) {
            // TODO @国际化处理@
            throw new CustomBusinessException("邮箱已存在");
        }
        String validCode = redisTemplate.opsForValue().get(StoreConstants.RedisConstants.getStoreEmailUpdateKey(userId));
        if (StrUtil.isBlank(validCode) || !StrUtil.equals(code, validCode)) {
            // TODO @国际化处理@
            throw new CustomBusinessException("验证码失效,请重新获取");
        }
        try {
            ResetEmailDTO emailDTO = new ResetEmailDTO();
            emailDTO.setUserId(userId);
            emailDTO.setEmail(email);
            R result = remoteTmsUpmsService.updateEmail(emailDTO);
            if (!result.isOk()) {
                // TODO @国际化处理@
                throw new CustomBusinessException("修改邮箱失败");
            }
        } catch (Exception e) {
            // TODO @国际化处理@
            throw new CustomBusinessException("修改邮箱失败");
        }
        TmsStoreCustomerEntity updateEntity = new TmsStoreCustomerEntity();
        updateEntity.setId(storeCustomerDB.getId());
        updateEntity.setEmail(email);
        return baseMapper.updateById(updateEntity) > 0;
    }

    @Override
    public boolean getPhoneCode(String phone) {
        if (!PhoneValidator.validatePhone(phone)) {
            // TODO @国际化处理@
            throw new CustomBusinessException("手机号不合法");
        }
        Long userId = SecurityUtils.getUser().getId();
        TmsStoreCustomerEntity tmsStoreCustomerEntity = baseMapper.getStoreCustomerByUserId(userId);
        if (null == tmsStoreCustomerEntity) {
            // TODO @国际化处理@
            throw new CustomBusinessException("门店客户不存在，不支持操作");
        }
        String code = RandomUtil.randomNumber(6);
        redisTemplate.opsForValue().set(StoreConstants.RedisConstants.getStorePhoneUpdateKey(userId),
                code, 5, TimeUnit.MINUTES);
        if (PhoneValidator.validateChinaMobile(phone)) {
            remoteTmsAppMobileService.sendSms(phone, code, Boolean.FALSE);
        } else if (PhoneValidator.validateCanadaMobile(phone)) {
            remoteTmsAppMobileService.sendSms(phone, code, Boolean.TRUE);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePhone(String phone, String code) {
        if (StrUtil.isBlank(phone) || StrUtil.isBlank(code)) {
            return false;
        }
        if (!PhoneValidator.validatePhone(phone)) {
            // TODO @国际化处理@
            throw new CustomBusinessException("手机号不合法");
        }

        Long userId = SecurityUtils.getUser().getId();
        TmsStoreCustomerEntity tmsStoreCustomerEntity = baseMapper.getStoreCustomerByUserId(userId);
        if (null == tmsStoreCustomerEntity) {
            // TODO @国际化处理@
            throw new CustomBusinessException("门店客户不存在，不支持操作");
        }
        if (PhoneValidator.validateChinaMobile(phone)) {
            phone = phone.replace("+86", "");
        } else if (PhoneValidator.validateCanadaMobile(phone)) {
            phone = phone.replace("+1", "");
        }

        String phoneDB = tmsStoreCustomerEntity.getPhone();
        if (phoneDB.equals(phone)) {
            return true;
        }
        TmsStoreCustomerEntity storeCustomerDB = baseMapper.getStoreCustomerByPhone(phone);
        if (null != storeCustomerDB) {
            // TODO @国际化处理@
            throw new CustomBusinessException("手机号已存在");
        }

        String validCode = redisTemplate.opsForValue().get(StoreConstants.RedisConstants.getStorePhoneUpdateKey(userId));
        if (StrUtil.isBlank(validCode) || !StrUtil.equals(code, validCode)) {
            // TODO @国际化处理@
            throw new CustomBusinessException("验证码失效,请重新获取");
        }
        try {
            ResetPhoneDTO phoneDTO = new ResetPhoneDTO();
            phoneDTO.setUserId(userId);
            phoneDTO.setPhone(phone);
            R result = remoteTmsUpmsService.updatePhone(phoneDTO);
            if (!result.isOk()) {
                // TODO @国际化处理@
                throw new CustomBusinessException("修改手机号失败");
            }
        } catch (Exception e) {
            // TODO @国际化处理@
            throw new CustomBusinessException("修改手机号失败");
        }

        TmsStoreCustomerEntity updateEntity = new TmsStoreCustomerEntity();
        updateEntity.setId(storeCustomerDB.getId());
        updateEntity.setPhone(phone);
        return baseMapper.updateById(updateEntity) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLoginPwd(ResetLoginPwdDTO resetLoginPwdDTO) {
        String phone = resetLoginPwdDTO.getPhone();
        TmsStoreCustomerEntity storeCustomerByPhone = baseMapper.getStoreCustomerByPhone(phone);
        if (null == storeCustomerByPhone) {
            // TODO @国际化处理@
            throw new CustomBusinessException("门店客户不存在，不支持操作");
        }
        Long userId = storeCustomerByPhone.getUserId();
        String newPassword = resetLoginPwdDTO.getNewPassword();
        String confirmPassword = resetLoginPwdDTO.getConfirmPassword();
        try {
            String decryptPassword = AESUtil.decrypt(newPassword);
            String decryptConfirmPassword = AESUtil.decrypt(confirmPassword);
            if (!decryptPassword.equals(decryptConfirmPassword)) {
                // TODO @国际化处理@
                throw new CustomBusinessException("两次输入密码不一致");
            }
            ResetPwdDTO resetPwdDTO = new ResetPwdDTO();
            resetPwdDTO.setUserId(userId);
            resetPwdDTO.setPassword(decryptPassword);
            R result = remoteTmsUpmsService.resetLoginPwd(resetPwdDTO);
            if (!result.isOk()) {
                // TODO @国际化处理@
                throw new CustomBusinessException("重置密码失败");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePwd(StorePwdChangeDTO storePwdChangeDTO) {
        String oldPassword = storePwdChangeDTO.getOldPassword();
        String newPassword = storePwdChangeDTO.getNewPassword();
        String confirmPassword = storePwdChangeDTO.getConfirmPassword();
        Long userId = SecurityUtils.getUser().getId();

        R<SysUser> result = remoteTmsUpmsService.getCustomerUserByUserId(SecurityUtils.getUser().getId());
        SysUser sysUser = result.getData();

        try {
            //解密后的原密码
            String decryptOldPassword = AESUtil.decrypt(oldPassword);
            //解密后的新密码
            String decryptNewPassword = AESUtil.decrypt(newPassword);
            //确认密码
            String decryptConfirmPassword = AESUtil.decrypt(confirmPassword);
            if (!decryptNewPassword.equals(decryptConfirmPassword)) {
                // TODO @国际化处理@
                throw new CustomBusinessException("两次输入密码不一致");
            }
            //校验原密码
            if (!ENCODER.matches(decryptOldPassword, AESUtil.decrypt(sysUser.getPassword()))) {
                throw new CustomBusinessException(LocalizedR.getMessage("old.password.incorrect", null));
            }
            ResetPwdDTO resetPwdDTO = new ResetPwdDTO();
            resetPwdDTO.setUserId(userId);
            resetPwdDTO.setPassword(decryptNewPassword);
            R reset = remoteTmsUpmsService.resetLoginPwd(resetPwdDTO);
            if (!reset.isOk()) {
                // TODO @国际化处理@
                throw new CustomBusinessException("重置密码失败");
            }
        } catch (Exception e) {
            // TODO @国际化处理@
            throw new CustomBusinessException("重置密码失败");

        }
        return true;
    }

    @Override
    public TmsStoreCustomerEntity getStoreCustomerByUserId(Long userId) {
        return baseMapper.getStoreCustomerByUserId(userId);
    }

    @Override
    public Long getStoreCustomerIdByUserId(Long userId) {
        return baseMapper.getStoreCustomerIdByUserId(userId);
    }

    @Override
    public TmsStoreCustomerEntity getStoreCustomerById(Long id) {
        if(null == id){
            return null;
        }
        return baseMapper.selectById(id);
    }

    @Override
    public R<IPage<TmsStoreCustomerAdminVo>> getTmsStoreCustomerAdminPage(Page page, TmsStoreCustomerPageVo tmsStoreCustomer) {
        // 构建查询条件
        MPJLambdaWrapper<TmsStoreCustomerEntity> wrapper = buildStoreCustomerQueryWrapper(tmsStoreCustomer);

        // 执行分页查询
        IPage<TmsStoreCustomerAdminVo> resultPage = this.baseMapper.selectJoinPage(page, TmsStoreCustomerAdminVo.class, wrapper);

        // 处理空值数据
        processNullValues(resultPage.getRecords());

        return R.ok(resultPage);
    }

    @Override
    public List<TmsStoreCustomerExcelVo> getTmsStoreCustomerAdminExcel(TmsStoreCustomerPageVo tmsStoreCustomer) {
        // 构建查询条件
        MPJLambdaWrapper<TmsStoreCustomerEntity> wrapper = buildStoreCustomerQueryWrapper(tmsStoreCustomer);

        // 执行查询并返回结果
        return this.baseMapper.selectJoinList(TmsStoreCustomerExcelVo.class, wrapper);
    }

    @Override
    public R removeBatchCustomer(Long[] ids) {
        // 查询对应的客户信息
        List<TmsStoreCustomerEntity> employeeList = this.listByIds(Arrays.asList(ids));
        List<Long> userIds = employeeList.stream().map(TmsStoreCustomerEntity::getUserId).collect(Collectors.toList());
        // 删除员工数据
        this.removeBatchByIds(Arrays.asList(ids));
        // 删除用户数据
        remoteTmsUpmsService.userDelNoToken(userIds.toArray(new Long[0]));
        return R.ok();
    }

    @Override
    public List<TmsStoreCustomerEntity> getAllCustomersByUser() {
        // 获取当前登录用户对应的门店信息
        TmsStoreUserEntity storeUser = tmsStoreUserService.getOne(new LambdaQueryWrapper<TmsStoreUserEntity>()
                .eq(TmsStoreUserEntity::getUserId, SecurityUtils.getUser().getId()), false);
        if (storeUser != null) {
            // 根据门店ID获取所有客户信息
            return this.list(new LambdaQueryWrapper<TmsStoreCustomerEntity>()
                    .eq(TmsStoreCustomerEntity::getStoreId, storeUser.getStoreId()));
        }
        return Collections.emptyList();
    }

    @Override
    public R register(SysUserAddDto dto) {
        R<SysUser> register = remoteUserService.register(dto);
        if (!register.isOk()) {
            return R.failed(register.getMsg());
        }
        SysUser user = register.getData();
        TmsStoreCustomerEntity storeUser = new TmsStoreCustomerEntity();
        storeUser.setUserId(user.getUserId());
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
        long snowflakeId = snowflakeIdGenerator.nextId();
        String shortId = String.valueOf(snowflakeId).substring(String.valueOf(snowflakeId).length() - 4); // 取后4位
        String customerCode = "KH" + datePart + shortId;
        storeUser.setCode(customerCode);
        storeUser.setName(user.getName());
        storeUser.setPhone(user.getPhone());
        storeUser.setLevel(0);
        storeUser.setType(0);
        storeUser.setStatus(1);
        save(storeUser);
        return R.ok();
    }

    @Override
    public R updateStoreCustomerPwd(TmsStoreCustomerDTO tmsStoreDTO) {
        //修改客户用户账号密码
        UserDTO userDTO = new UserDTO();
        userDTO.setUserId(tmsStoreDTO.getUserId());
        userDTO.setPassword("123456");
        //更新用户的密码
        R r = remoteTmsUpmsService.updateCustomerInfo(userDTO);
        if (r.getCode() == 0) {
            //成功修改
            return R.ok();
        } else {
            throw new CustomBusinessException(LocalizedR.getMessage("old.password.incorrect", null));
        }
    }



    /**
     * 获取当前门店客户
     *
     * @return
     */
    @Override
    public TmsStoreCustomerEntity getCurrentCustomer() {
        Long userId = Optional.ofNullable(SecurityUtils.getUser())
                .map(JynxUser::getId)
                .orElseThrow(() -> new RuntimeException("invalid user"));

        LambdaQueryWrapper<TmsStoreCustomerEntity> wrapper = Wrappers.lambdaQuery(TmsStoreCustomerEntity.class)
                .eq(TmsStoreCustomerEntity::getUserId, userId);

        return getOneOpt(wrapper)
                .orElseThrow(() -> new RuntimeException("invalid store user"));

    }

    /**
     * 构建门店客户查询条件
     *
     * @param tmsStoreCustomer 查询参数
     * @return 查询包装器
     */
    private MPJLambdaWrapper<TmsStoreCustomerEntity> buildStoreCustomerQueryWrapper(TmsStoreCustomerPageVo tmsStoreCustomer) {
        MPJLambdaWrapper<TmsStoreCustomerEntity> wrapper = new MPJLambdaWrapper<>();
        return wrapper.selectAll(TmsStoreCustomerEntity.class)
                // 关联门店表
                .leftJoin(TmsStoreEntity.class, TmsStoreEntity::getId, TmsStoreCustomerEntity::getStoreId)
                .selectAs(TmsStoreEntity::getStoreName, TmsStoreCustomerAdminVo::getStoreName)
                // 关联余额表
                .leftJoin(TmsStoreBalanceEntity.class, TmsStoreBalanceEntity::getStoreCustomerId, TmsStoreCustomerEntity::getId)
                .selectAs(TmsStoreBalanceEntity::getAmount, TmsStoreCustomerAdminVo::getAmount)
                // 查询条件
                .like(StrUtil.isNotBlank(tmsStoreCustomer.getCustomerCode()), TmsStoreCustomerEntity::getCode, tmsStoreCustomer.getCustomerCode())
                .like(StrUtil.isNotBlank(tmsStoreCustomer.getCustomerName()), TmsStoreCustomerEntity::getName, tmsStoreCustomer.getCustomerName())
                .eq(tmsStoreCustomer.getStatus() != null, TmsStoreCustomerEntity::getStatus, tmsStoreCustomer.getStatus())
                .between(ObjectUtil.isNotNull(tmsStoreCustomer.getBeginTime()) && ObjectUtil.isNotNull(tmsStoreCustomer.getEndTime()),
                        TmsStoreCustomerEntity::getCreateTime, tmsStoreCustomer.getBeginTime(), tmsStoreCustomer.getEndTime())
                // 排序
                .orderByAsc(TmsStoreCustomerEntity::getCreateTime);
    }

    /**
     * 处理查询结果中的空值
     *
     * @param records 查询结果列表
     */
    private void processNullValues(List<TmsStoreCustomerAdminVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }

        records.forEach(vo -> {
            if (vo.getAmount() == null) {
                vo.setAmount(BigDecimal.ZERO);
            }
            if (vo.getStoreName() == null) {
                vo.setStoreName("");
            }
        });
    }


}
