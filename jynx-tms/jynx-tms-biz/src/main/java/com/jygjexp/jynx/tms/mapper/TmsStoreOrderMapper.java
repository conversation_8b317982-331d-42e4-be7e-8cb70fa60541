package com.jygjexp.jynx.tms.mapper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.data.datascope.JynxBaseMapper;
import com.jygjexp.jynx.tms.constants.StoreEnums;
import com.jygjexp.jynx.tms.dto.StoreOrderQueryDTO;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderEntity;
import com.jygjexp.jynx.tms.vo.StoreOrderVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Mapper
public interface TmsStoreOrderMapper extends JynxBaseMapper<TmsStoreOrderEntity> {

    IPage<StoreOrderVO> getTmsStoreOrderStorePage(Page page, StoreOrderQueryDTO storeOrderQueryDTO, Long storeId);

    IPage<StoreOrderVO> getTmsStoreOrderAdminPage(Page page, StoreOrderQueryDTO storeOrderQueryDTO);

    default void deleteByMainEntrustedNo(String mainEntrustedOrder){
        if(StrUtil.isBlank(mainEntrustedOrder)){
            return;
        }
        LambdaQueryWrapper<TmsStoreOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsStoreOrderEntity::getMainEntrustedOrder, mainEntrustedOrder);
        queryWrapper.eq(TmsStoreOrderEntity::getSubFlag, StoreEnums.StoreOrder.SubFlag.SUB.getValue());
        delete(queryWrapper);
    }

    default void updateOrderStatusByMainEntrustedNo(String mainEntrustedOrder,Integer newOrderStatus){
        if(StrUtil.isBlank(mainEntrustedOrder)){
            return;
        }
        LambdaUpdateWrapper<TmsStoreOrderEntity> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.set(TmsStoreOrderEntity::getOrderStatus,newOrderStatus);
        queryWrapper.eq(TmsStoreOrderEntity::getMainEntrustedOrder, mainEntrustedOrder);
        update(queryWrapper);
    }


    default Map<String,TmsStoreOrderEntity> getSubOrderMapByMainEntrustedOrder(String mainEntrustedOrder){
        if(StrUtil.isBlank(mainEntrustedOrder)){
            return null;
        }
        LambdaQueryWrapper<TmsStoreOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsStoreOrderEntity::getMainEntrustedOrder, mainEntrustedOrder);
        queryWrapper.eq(TmsStoreOrderEntity::getSubFlag, StoreEnums.StoreOrder.SubFlag.SUB.getValue());
        List<TmsStoreOrderEntity> storeOrderEntities = selectList(queryWrapper);
        if(CollUtil.isEmpty(storeOrderEntities)){
            return null;
        }
        return storeOrderEntities
                .stream()
                .collect(Collectors.toMap(TmsStoreOrderEntity::getEntrustedOrderNumber, Function.identity(),(v1, v2)->v1));
    }

    default List<TmsStoreOrderEntity> getSubOrderByMainEntrustedOrder(String mainEntrustedOrder){
        if(StrUtil.isBlank(mainEntrustedOrder)){
            return null;
        }
        LambdaQueryWrapper<TmsStoreOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsStoreOrderEntity::getMainEntrustedOrder, mainEntrustedOrder);
        queryWrapper.eq(TmsStoreOrderEntity::getSubFlag, StoreEnums.StoreOrder.SubFlag.SUB.getValue());
        List<TmsStoreOrderEntity> storeOrderEntities = selectList(queryWrapper);
        if(CollUtil.isEmpty(storeOrderEntities)){
            return null;
        }
        return storeOrderEntities;
    }


    default Long getSubOrderCount(){
        LambdaQueryWrapper<TmsStoreOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsStoreOrderEntity::getSubFlag, StoreEnums.StoreOrder.SubFlag.SUB.getValue());
        return selectCount(queryWrapper);
    }


}
