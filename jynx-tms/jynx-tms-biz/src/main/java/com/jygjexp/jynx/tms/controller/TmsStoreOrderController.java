package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.dto.ChannelPriceQueryDTO;
import com.jygjexp.jynx.tms.dto.StoreOrderDTO;
import com.jygjexp.jynx.tms.dto.StoreOrderQueryDTO;
import com.jygjexp.jynx.tms.dto.SuggestStoreQueryDTO;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderEntity;
import com.jygjexp.jynx.tms.service.TmsStoreOrderService;
import com.jygjexp.jynx.tms.vo.StoreOrderVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 快递客户订单
 *
 * <AUTHOR>
 * @date 2025-07-14 17:34:55
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStoreOrder" )
@Tag(description = "tmsStoreOrder" , name = "快递客户订单管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStoreOrderController {
    private final TmsStoreOrderService service;
    /**
     * 分页查询
     * @param page 分页对象
     * @param storeOrderQueryDTO 快递客户订单
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    //@PreAuthorize("@pms.hasPermission('tms_tmsStoreOrder_view')" )
    public R selectPage(@ParameterObject Page page, @ParameterObject StoreOrderQueryDTO storeOrderQueryDTO) {
        // 主单维度
        return R.ok(service.selectPage(page, storeOrderQueryDTO));
    }
    /**
     * 通过id查询快递客户订单
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    //@PreAuthorize("@pms.hasPermission('tms_tmsStoreOrder_view')" )
    public R getStoreOrderById(@PathVariable("id" ) Long id) {
        return R.ok(service.getStoreOrderById(id));
    }

    /**
     * 新增快递客户订单
     * @param storeOrderDTO 快递客户订单
     * @return R
     */
    @Operation(summary = "新增快递客户订单" , description = "新增快递客户订单" )
    @SysLog("新增快递客户订单" )
    @PostMapping("/add")
    //@PreAuthorize("@pms.hasPermission('tms_tmsStoreOrder_add')" )
    public R saveStoreOrder(@RequestBody @Valid StoreOrderDTO storeOrderDTO) {
        return service.saveStoreOrder(storeOrderDTO) ? R.ok(): R.failed();
    }

    /**
     * 导入货物
     * @param file 导入货物
     * @return R
     */
    @Operation(summary = "导入快递客户货物" , description = "导入快递客户货物" )
    @SysLog("导入快递客户货物" )
    @PostMapping("/import_goods")
    //@PreAuthorize("@pms.hasPermission('tms_tmsStoreOrder_add')" )
    public R importStoreGoods(@RequestParam("file") MultipartFile file) {
        return R.ok(service.importStoreGoods(file));
    }
    /**
     * 服务商询价
     */
    @Operation(summary = "服务商询价" , description = "服务商询价" )
    @PostMapping("/getPrice")
    //@PreAuthorize("@pms.hasPermission('tms_tmsStoreOrder_getPrice')")
    public R getChannelPrice(@RequestBody ChannelPriceQueryDTO channelPriceQueryDTO){
        return R.ok(service.getChannelPrice(channelPriceQueryDTO));
    }
    /**
     * 服务商询价
     */
    @Operation(summary = "获取推荐门店" , description = "获取推荐门店" )
    @GetMapping("/getSuggestStore")
    //@PreAuthorize("@pms.hasPermission('tms_tmsStoreOrder_getPrice')")
    public R getSuggestStore(@ParameterObject SuggestStoreQueryDTO storeQueryDTO){
        return R.ok(service.getSuggestStore(storeQueryDTO));
    }

    /**
     * 打印凭证
     */
    @Operation(summary = "打印凭证" , description = "打印凭证" )
    @SysLog(value = "打印凭证")
    @GetMapping("/print_script")
    //@PreAuthorize("@pms.hasPermission('tms_tmsStoreOrder_getPrice')")
    public void printScript(@RequestParam Long id,HttpServletResponse response){
        service.printScript(id,response);
    }


    /**
     * 修改快递客户订单  -暂不支持
     * @param storeOrderDTO 快递客户订单
     * @return R
     */
    @Operation(summary = "修改 快递客户订单" , description = "修改快递客户订单" )
    @SysLog("修改快递客户订单" )
    @PutMapping
    //@PreAuthorize("@pms.hasPermission('tms_tmsStoreOrder_edit')" )
    public R updateStoreOrderById(@RequestBody StoreOrderDTO storeOrderDTO) {
        return service.updateStoreOrderById(storeOrderDTO) ? R.ok():R.failed();
    }

    /**
     * 通过id删除快递客户订单
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除快递客户订单" , description = "通过id删除快递客户订单" )
    @SysLog("通过id删除快递客户订单" )
    @DeleteMapping
    //@PreAuthorize("@pms.hasPermission('tms_tmsStoreOrder_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(service.removeBatchByIds(CollUtil.toList(ids)));
    }

    /**
     * 取消订单接口
     */
    @Operation(summary = "取消订单接口" , description = "取消订单接口" )
    @SysLog("取消订单接口" )
    @PostMapping("/cancel")
    //@PreAuthorize("@pms.hasPermission('tms_tmsStoreOrder_cancel')" )
    public R cancelOrder(@RequestParam("id") Long id) {
        return service.cancelOrder(id) ? R.ok():R.failed();
    }

    /**
     * 导出excel 表格
     * @param tmsStoreOrder 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    //@PreAuthorize("@pms.hasPermission('tms_tmsStoreOrder_export')" )
    public List<TmsStoreOrderEntity> export(TmsStoreOrderEntity tmsStoreOrder,Long[] ids) {
        return service.list(Wrappers.lambdaQuery(tmsStoreOrder).in(ArrayUtil.isNotEmpty(ids), TmsStoreOrderEntity::getId, ids));
    }

    /**
     * 门店端分页查询
     */
    @SysLog("门店端分页查询")
    @Operation(summary = "门店端分页查询", description = "门店端分页查询")
    @PostMapping("/storePage")
//    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrder_store_view')")
    public R<IPage<StoreOrderVO>> getTmsStoreOrderStorePage(@RequestBody StoreOrderQueryDTO storeOrderQueryDTO) {
        return R.ok(service.getTmsStoreOrderStorePage(storeOrderQueryDTO));
    }

    /**
     * 管理端分页查询
     */
    @SysLog("管理端分页查询")
    @Operation(summary = "管理端分页查询", description = "管理端分页查询")
    @PostMapping("/adminPage")
//    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrder_admin_view')")
    public R<IPage<StoreOrderVO>> getTmsStoreOrderAdminPage( @RequestBody StoreOrderQueryDTO storeOrderQueryDTO) {
        return R.ok(service.getTmsStoreOrderAdminPage(storeOrderQueryDTO));
    }

    /**
     * 批量导入快递订单信息
     */
    @SysLog("批量导入快递订单信息")
    @Operation(summary = "批量导入快递订单信息", description = "批量导入快递订单信息")
    @PostMapping("/importCargoInfo")
//    @PreAuthorize("@pms.hasPermission('tms_tmsStoreOrder_importCargoInfo')")
    public R importCargoInfo(@RequestParam("file") MultipartFile file) {
        return service.importCargoInfo(file);
    }

    /**
     * 核销订单
     */
    @SysLog("核销订单")
    @Operation(summary = "核销订单", description = "核销订单")
    @PutMapping("/writeOff/{id}")
    public R writeOffOrder(@PathVariable("id" ) Long id){
        return service.writeOffOrder(id) ? R.ok():R.failed();
    }

}
