package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceOfflineRecharge;

public interface TmsStoreBalanceService extends IService<TmsStoreBalanceEntity> {

    /**
     * 线下充值
     *
     * @param offlineRecharge
     * @return
     */
    Boolean offlineRecharge(Long id, TmsStoreBalanceOfflineRecharge offlineRecharge);

    /**
     * 查询当前用户的余额
     *
     * @return
     */
    TmsStoreBalanceEntity getCurrent();

    /**
     * 初始化所有客户的余额
     *
     * @return
     */
    Boolean init();

}
